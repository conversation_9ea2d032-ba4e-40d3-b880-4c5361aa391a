<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="@dimen/space_30"
    android:background="@drawable/btn_bg_delete_plan_normal"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <dji.sampleV5.aircraft.mvvm.widget.view.TagLayout
        android:id="@+id/dayTags"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/space_16"
        app:horizontalSpace="@dimen/space_16"
        app:verticalSpace="@dimen/space_16"
        app:tagTextSize="@dimen/text_size_18"
        app:tagTextColor="@color/selector_label_text"
        app:tagBackground="@drawable/selector_label_tag"
        app:tagTextHorizontalPadding="@dimen/space_30"
        app:tagTextVerticalPadding="@dimen/space_10"
        app:layout_constraintTop_toTopOf="parent"
        app:tagMinWidth="@dimen/space_50"
        app:tagSelectMode="multiple"/>

</androidx.constraintlayout.widget.ConstraintLayout>