<?xml version="1.0" encoding="utf-8"?>
<dji.sampleV5.aircraft.mvvm.widget.CustomDrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/plan_drawer"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".mvvm.ui.waypoint.MissionListActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cons_parent"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/mission_toolbar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_40"
            android:background="@color/black_gray"
            android:elevation="@dimen/space_5"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
            app:contentInsetStartWithNavigation="0dp"
            app:layout_constraintBottom_toTopOf="@+id/condition_bar"
            app:layout_constraintTop_toTopOf="parent"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light">

            <!--返回按钮-->
            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/btn_back"
                android:layout_width="@dimen/space_40"
                android:layout_height="@dimen/space_40"
                android:layout_gravity="center_vertical"
                android:padding="@dimen/space_10"
                android:src="@drawable/uxsdk_ic_back_arrow" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/toolbarTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="计划任务"
                android:textColor="@android:color/white"
                android:textSize="@dimen/text_size_18" />

            <!--新建查询按钮-->
            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/btn_open"
                android:layout_width="@dimen/space_40"
                android:layout_height="@dimen/space_40"
                android:layout_gravity="center_vertical|end"
                android:layout_marginEnd="@dimen/space_10"
                android:padding="@dimen/space_10"
                android:rotation="0"
                android:src="@drawable/ic_add_plan" />

        </androidx.appcompat.widget.Toolbar>

        <RelativeLayout
            android:id="@+id/condition_bar"
            android:layout_width="0dp"
            android:layout_height="@dimen/space_50"
            android:animateLayoutChanges="true"
            android:background="@color/white"
            android:elevation="@dimen/space_5"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@+id/mission_toolbar"
            android:paddingVertical="@dimen/space_5"
            app:layout_constraintBottom_toTopOf="@+id/mission_refresh"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:visibility="visible">

            <dji.sampleV5.aircraft.mvvm.widget.spinner.EditSpinner
                android:id="@+id/site_selector_sp"
                android:layout_width="@dimen/space_200"
                android:layout_height="@dimen/space_40"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/space_20"
                android:gravity="center_vertical"
                android:minWidth="@dimen/space_100"
                app:es_arrowImage="@drawable/ms_ic_arrow_down_gray"
                app:es_background="@drawable/bg_linear_choose_date_selected"
                app:es_dropdown_bg="@drawable/white_bg_spinner_pop"
                app:es_height="@dimen/space_40"
                app:es_hint="请选择站点"
                app:es_isFilterKey="true"
                app:es_maxLength="20"
                app:es_textColor="@color/black_gray"
                app:es_textSize="@dimen/uxsdk_text_size_15sp" />

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/linear_effect_scope"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginStart="19dp"
                android:layout_toEndOf="@+id/site_selector_sp"
                android:background="@drawable/bg_linear_choose_date_selected"
                android:orientation="horizontal">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/btn_start_time"
                    android:layout_width="@dimen/space_200"
                    android:layout_height="@dimen/space_40"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:hint="开始时间"
                    android:singleLine="true"
                    android:textColor="@color/black_gray"
                    android:textColorHint="@color/white_gray"
                    android:textSize="@dimen/text_size_15" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:layout_marginHorizontal="@dimen/space_5"
                    android:layout_weight="1"
                    android:src="@drawable/ic_break_line" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/btn_end_time"
                    android:layout_width="@dimen/space_200"
                    android:layout_height="@dimen/space_40"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:hint="结束时间"
                    android:singleLine="true"
                    android:textColor="@color/black_gray"
                    android:textColorHint="@color/white_gray"
                    android:textSize="@dimen/text_size_15" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/btn_clear_date"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginHorizontal="@dimen/space_10"
                    android:src="@drawable/ic_date_clear"
                    android:visibility="gone" />

            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btn_search"
                android:layout_width="@dimen/space_80"
                android:layout_height="@dimen/space_40"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/space_30"
                android:layout_toEndOf="@+id/linear_effect_scope"
                android:background="@drawable/mission_btn_selector_blue"
                android:drawableStart="@drawable/ic_inquire"
                android:gravity="center"
                android:paddingStart="@dimen/space_10"
                android:text="查 询"
                android:textColor="@color/white"
                android:textSize="@dimen/text_size_15" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btn_create"
                android:layout_width="@dimen/space_80"
                android:layout_height="@dimen/space_40"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/space_30"
                android:layout_marginEnd="@dimen/space_20"
                android:background="@drawable/mission_btn_selector_blue"
                android:drawableStart="@drawable/ic_create_new"
                android:gravity="center"
                android:paddingStart="@dimen/space_10"
                android:text="新 建"
                android:textColor="@color/white"
                android:textSize="@dimen/text_size_15" />

        </RelativeLayout>

        <com.drake.brv.PageRefreshLayout
            android:id="@+id/mission_refresh"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/condition_bar">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/mission_list_rv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:elevation="@dimen/space_5"
                tools:layoutManager="GridLayoutManager"
                tools:listitem="@layout/plan_mission_list_item_layout"
                tools:spanCount="2" />
        </com.drake.brv.PageRefreshLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="550dp"
        android:layout_height="match_parent"
        android:layout_gravity="end"
        android:descendantFocusability="afterDescendants">

        <include
            android:id="@+id/plan_detail_layout"
            layout="@layout/plan_mission_dialog_layout" />

    </androidx.appcompat.widget.LinearLayoutCompat>
</dji.sampleV5.aircraft.mvvm.widget.CustomDrawerLayout>