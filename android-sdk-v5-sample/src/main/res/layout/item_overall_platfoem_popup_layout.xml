<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/item_platform"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/uxsdk_10_dp"
    android:background="@drawable/public_dialog_bg"
    android:elevation="@dimen/uxsdk_5_dp">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_platform_ic"
        android:layout_width="@dimen/uxsdk_80_dp"
        android:layout_height="@dimen/uxsdk_80_dp"
        android:layout_margin="@dimen/uxsdk_10_dp"
        app:layout_constraintBottom_toTopOf="@+id/tv_platform_name"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@mipmap/skysys_xd_app_icon" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_platform_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginVertical="@dimen/uxsdk_10_dp"
        android:textColor="@color/black"
        android:textSize="@dimen/uxsdk_dic_text_size_20sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/iv_platform_ic"
        app:layout_constraintStart_toStartOf="@+id/iv_platform_ic"
        app:layout_constraintTop_toBottomOf="@+id/iv_platform_ic"
        tools:text="驭光" />
</androidx.constraintlayout.widget.ConstraintLayout>