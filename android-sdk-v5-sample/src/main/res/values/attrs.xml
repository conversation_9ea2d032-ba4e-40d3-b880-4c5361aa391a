<?xml version="1.0" encoding="utf-8"?>
<resources>


    <declare-styleable name="CapOrRecView">
        <attr name="candidatePic" format="reference|color" />
        <attr name="actionOffColor" format="color" />
        <attr name="isCandidate" format="boolean" />
        <attr name="actionOnPic" format="reference|color" />
    </declare-styleable>

    <declare-styleable name="SlideLockView">
        <attr name="lock_drawable" format="reference" />
        <attr name="lock_radius" format="dimension|reference" />
        <attr name="lock_tips_tx" format="string|reference" />
        <attr name="locl_tips_tx_size" format="dimension|reference" />
        <attr name="lock_tips_tx_color" format="color|reference" />
    </declare-styleable>

    <declare-styleable name="CircleImageView">
        <attr name="civ_circle_background_color" format="color" />
    </declare-styleable>

    <declare-styleable name="CameraView">
        <!--
          Set this to true if you want the CameraView to adjust its bounds to preserve the aspect
          ratio of its camera preview.
        -->
        <attr name="android:adjustViewBounds"/>
        <!-- Direction the camera faces relative to device screen. -->
        <attr name="facing" format="enum">
            <!-- The camera device faces the opposite direction as the device's screen. -->
            <enum name="back" value="0"/>
            <!-- The camera device faces the same direction as the device's screen. -->
            <enum name="front" value="1"/>
        </attr>
        <!-- Aspect ratio of camera preview and pictures. -->
        <attr name="aspectRatio" format="string"/>
        <!-- Continuous auto focus mode. -->
        <attr name="autoFocus" format="boolean"/>
        <!-- The flash mode. -->
        <attr name="flash" format="enum">
            <!-- Flash will not be fired. -->
            <enum name="off" value="0"/>
            <!--
              Flash will always be fired during snapshot.
              The flash may also be fired during preview or auto-focus depending on the driver.
            -->
            <enum name="on" value="1"/>
            <!--
              Constant emission of light during preview, auto-focus and snapshot.
              This can also be used for video recording.
            -->
            <enum name="torch" value="2"/>
            <!--
              Flash will be fired automatically when required.
              The flash may be fired during preview, auto-focus, or snapshot depending on the
              driver.
            -->
            <enum name="auto" value="3"/>
            <!--
              Flash will be fired in red-eye reduction mode.
            -->
            <enum name="redEye" value="4"/>
        </attr>
    </declare-styleable>

    <declare-styleable name="RoundRelativeLayout">
        <attr name="radius" format="dimension" />
    </declare-styleable>

    <declare-styleable name="PullToRefresh">
        <!-- A drawable to use as the background of the Refreshable View -->
        <attr name="ptrRefreshableViewBackground" format="reference|color" />

        <!-- A drawable to use as the background of the Header and Footer Loading Views -->
        <attr name="ptrHeaderBackground" format="reference|color" />

        <!-- Text Color of the Header and Footer Loading Views -->
        <attr name="ptrHeaderTextColor" format="reference|color" />

        <!-- Text Color of the Header and Footer Loading Views Sub Header -->
        <attr name="ptrHeaderSubTextColor" format="reference|color" />

        <!-- Mode of Pull-to-Refresh that should be used -->
        <attr name="ptrMode">
            <flag name="disabled" value="0x0" />
            <flag name="pullFromStart" value="0x1" />
            <flag name="pullFromEnd" value="0x2" />
            <flag name="both" value="0x3" />
            <flag name="manualOnly" value="0x4" />

            <!-- These last two are depreacted -->
            <flag name="pullDownFromTop" value="0x1" />
            <flag name="pullUpFromBottom" value="0x2" />
        </attr>

        <!-- Whether the Indicator overlay(s) should be used -->
        <attr name="ptrShowIndicator" format="reference|boolean" />

        <!-- Drawable to use as Loading Indicator. Changes both Header and Footer. -->
        <attr name="ptrDrawable" format="reference" />

        <!-- Drawable to use as Loading Indicator in the Header View. Overrides value set in ptrDrawable. -->
        <attr name="ptrDrawableStart" format="reference" />

        <!-- Drawable to use as Loading Indicator in the Footer View. Overrides value set in ptrDrawable. -->
        <attr name="ptrDrawableEnd" format="reference" />

        <!-- Whether Android's built-in Over Scroll should be utilised for Pull-to-Refresh. -->
        <attr name="ptrOverScroll" format="reference|boolean" />

        <!-- Base text color, typeface, size, and style for Header and Footer Loading Views -->
        <attr name="ptrHeaderTextAppearance" format="reference" />

        <!-- Base text color, typeface, size, and style for Header and Footer Loading Views Sub Header -->
        <attr name="ptrSubHeaderTextAppearance" format="reference" />

        <!-- Style of Animation should be used displayed when pulling. -->
        <attr name="ptrAnimationStyle">
            <flag name="rotate" value="0x0" />
            <flag name="flip" value="0x1" />
        </attr>

        <!-- Whether the user can scroll while the View is Refreshing -->
        <attr name="ptrScrollingWhileRefreshingEnabled" format="reference|boolean" />

        <!--
        	Whether PullToRefreshListView has it's extras enabled. This allows the user to be
        	able to scroll while refreshing, and behaves better. It acheives this by adding
        	Header and/or Footer Views to the ListView.
        -->
        <attr name="ptrListViewExtrasEnabled" format="reference|boolean" />

        <!--
        	Whether the Drawable should be continually rotated as you pull. This only
        	takes effect when using the 'Rotate' Animation Style.
        -->
        <attr name="ptrRotateDrawableWhilePulling" format="reference|boolean" />

        <!-- BELOW HERE ARE DEPRECEATED. DO NOT USE. -->
        <attr name="ptrAdapterViewBackground" format="reference|color" />
        <attr name="ptrDrawableTop" format="reference" />
        <attr name="ptrDrawableBottom" format="reference" />
    </declare-styleable>


    <declare-styleable name="customer_spinner">
        <!-- Reference to an array resource that will populate a list/adapter. -->
        <attr name="android:entries"/>
    </declare-styleable>


    <attr name="EditSpinnerStyle" format="reference" />

    <!--EditSpinner-->
    <declare-styleable name="EditSpinner">
        <!--输入提示-->
        <attr name="es_hint" format="string" />
        <!--输入框的高度-->
        <attr name="es_height" format="dimension" />
        <!--输入框的字体大小-->
        <attr name="es_textSize" format="dimension" />
        <!--输入框的字体颜色-->
        <attr name="es_textColor" format="color" />
        <!--箭头图标的margin-->
        <attr name="es_arrowMargin" format="dimension" />
        <!--箭头图标的资源-->
        <attr name="es_arrowImage" format="reference" />
        <!--输入框的背景-->
        <attr name="es_background" format="reference" />
        <attr name="es_maxLine" format="integer" />
        <!--默认可选性内容-->
        <attr name="es_entries" format="reference" />
        <!--下拉框 下拉条目的背景-->
        <attr name="es_dropdown_bg" format="reference" />
        <!--输入下拉框是否可编辑, 默认true-->
        <attr name="es_enable" format="boolean" />
        <!--输入框组件是否可编辑, 默认false-->
        <attr name="es_input_disable" format="boolean" />
        <!--输入下拉框可输入的最大字符长度-->
        <attr name="es_maxLength" format="integer" />
        <!--输入下拉框可输入的最大字符宽度-->
        <attr name="es_maxEms" format="integer" />
        <!--输入下拉框在输入时是否显示筛选到的信息，默认true-->
        <attr name="es_isShowFilterData" format="boolean" />
        <!--输入下拉框显示筛选信息时，是否显示key为醒目的颜色，默认false-->
        <attr name="es_isFilterKey" format="boolean" />
        <!--弹出窗的动画样式-->
        <attr name="es_popAnimStyle" format="reference" />
    </declare-styleable>

    <declare-styleable name="CellEditWidget">
        <attr name="uxsdk_summary" />
        <attr name="uxsdk_unit" format="string|reference" />
        <attr name="uxsdk_layout" />
        <attr name="uxsdk_value" />
        <attr name="uxsdk_number" format="boolean" />
        <attr name="uxsdk_minValue" format="integer" />
        <attr name="uxsdk_maxValue" format="integer" />
        <attr name="uxsdk_tipsTextSize" format="dimension" />
        <attr name="uxsdk_summaryTextSize" />
        <attr name="uxsdk_titleTextSize" />
        <attr name="uxsdk_summaryColor" />
        <attr name="uxsdk_editorColor" format="color|reference" />
        <attr name="uxsdk_editorBg" format="reference" />
        <attr name="uxsdk_editorWidth" format="dimension" />
        <attr name="uxsdk_gravityCenter" format="boolean" />
    </declare-styleable>

    <declare-styleable name="HorizontalSeekBar">
        <attr name="seekbar_minValueVisible" format="boolean" />
        <attr name="seekbar_maxValueVisible" format="boolean" />
        <attr name="seekbar_minusVisible" format="boolean" />
        <attr name="seekbar_plusVisible" format="boolean" />
        <!--        <attr name="seekbar_maxValue" format="integer" />-->
        <!--        <attr name="seekbar_minValue" format="integer" />-->
    </declare-styleable>

    <declare-styleable name="MaxCharsEditWidget">
        <attr name="background" format="reference" />
        <attr name="maxChars" format="integer" />
        <attr name="maxChars_hint" format="string" />
    </declare-styleable>

    <declare-styleable name="AlignedTextView">
        <attr name="alignment" format="integer" />
    </declare-styleable>

    <declare-styleable name="TagLayout">
        <attr name="horizontalSpace" format="dimension" /><!-- tag之间的横向间距-->
        <attr name="verticalSpace" format="dimension" /><!-- tag之间的纵向间距-->
        <attr name="maxLines" format="integer" /><!-- 最大行数-->
        <attr name="tagResId" format="reference" /><!-- tag TextView资源id，可将tag文字大小、颜色、背景、padding等单独配置提升复用性-->
        <attr name="tagTextSize" format="dimension" /><!-- tag文字大小-->
        <attr name="tagBackground" format="reference" /><!-- tag背景-->
        <attr name="tagMinWidth" format="dimension" /><!-- tag最小宽度-->
        <attr name="tagTextColor" format="color" /><!-- tag文字颜色-->
        <attr name="tagTextHorizontalPadding" format="dimension" /><!-- tag 内部横向padding-->
        <attr name="tagTextVerticalPadding" format="dimension" /><!-- tag 内部纵向padding-->
        <attr name="maximumSelectionCount" format="integer" /><!-- 设置最多能够选择的个数-->
        <attr name="tagSelectMode" format="enum"><!-- 单选 多选-->
            <enum name="single" value="1"></enum><!--单选-->
            <enum name="multiple" value="2"></enum><!--多选-->
            <enum name="none" value="0"></enum><!--不可选-->
        </attr>
        <!--以下配置建议在列表中提高性能使用-->
        <attr name="cacheMode" format="enum"><!-- 缓存方式，常用方式下没有影响，当tag需要在RecycleView或者ListView中显示，设置为lazy能显著提高性能-->
            <enum name="auto" value="0"></enum><!--自动根据当前tag数量来增删childView，默认方式-->
            <enum name="lazy" value="1"></enum><!--tag数量会根据最大tag数量来决定，大于会等于tag数量的childView自动隐藏，而不是删除，性能最佳-->
            <enum name="none" value="2"></enum><!--不使用缓存-->
        </attr>
        <attr name="maxTags" format="integer" /><!--tags最大显示数量-->
        <attr name="preCache" format="boolean" /><!-- 预缓存，初始化时预先添加一定数量的childView-->

    </declare-styleable>

    <declare-styleable name="ManualZoomWidgetView">
        <attr name="lineColor" format="color" />
        <attr name="markerColor" format="color" />
        <attr name="cornerRadius" format="dimension"/>
        <attr name="backgroundRadius" format="dimension"/>
        <attr name="indicatorColor" format="color" />
        <attr name="scaleDrawable" format="reference" />
    </declare-styleable>

    <!--相机快门参数设置-->
    <declare-styleable name="CameraSettingWidget">
        <!-- 已有的属性 -->
        <attr name="titleStyle" format="reference" />
        <attr name="scrollStyle" format="reference" />
        <attr name="indicatorStyle" format="reference" />

        <!-- 新增的属性 -->
        <attr name="title" format="string" />
        <attr name="titleColor" format="color" />
        <attr name="titleSize" format="dimension" />
        <attr name="titleVisibility" format="enum">
            <enum name="visible" value="0" />
            <enum name="invisible" value="1" />
            <enum name="gone" value="2" />
        </attr>
        <attr name="values" format="reference" />
        <attr name="defaultValue" format="string" />
    </declare-styleable>


    <attr name="visibleCount" format="integer" />
    <attr name="isLoop" format="boolean" />
    <attr name="scaleX" format="float" />
    <attr name="scaleY" format="float" />
    <attr name="alpha" format="float" />

    <attr name="dividerVisible" format="boolean" />
    <attr name="dividerColor" format="color" />
    <attr name="dividerSize" format="dimension" />
    <attr name="dividerMargin" format="dimension" />

    <attr name="selectedTextColor" format="color|reference" />
    <attr name="unSelectedTextColor" format="color|reference" />
    <attr name="selectedTextSize" format="dimension" />
    <attr name="unSelectedTextSize" format="dimension" />
    <attr name="selectedIsBold" format="boolean" />

    <declare-styleable name="PickerRecyclerView">
        <attr name="orientation" format="enum">
            <enum name="horizontal" value="0" />
            <enum name="vertical" value="1" />
        </attr>
        <attr name="visibleCount" />
        <attr name="isLoop" />
        <attr name="scaleX" />
        <attr name="scaleY" />
        <attr name="alpha" />

        <attr name="dividerVisible"/>
        <attr name="dividerColor" />
        <attr name="dividerSize" />
        <attr name="dividerMargin" />
    </declare-styleable>

    <declare-styleable name="TextPickerView">
        <attr name="selectedTextColor" />
        <attr name="unSelectedTextColor" />
        <attr name="selectedTextSize" />
        <attr name="unSelectedTextSize" />
        <attr name="selectedIsBold" />
    </declare-styleable>

    <!--水平进度条-->
    <declare-styleable name="CustomSeekBar">
        <attr name="inputType" format="integer">
            <!-- There is no content type.  The text is not editable. -->
            <flag name="none" value="0x00000000" />
            <!-- Just plain old text.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_TEXT} |
                 {@link android.text.InputType#TYPE_TEXT_VARIATION_NORMAL}. -->
            <flag name="text" value="0x00000001" />
            <!-- Can be combined with <var>text</var> and its variations to
                 request capitalization of all characters.  Corresponds to
                 {@link android.text.InputType#TYPE_TEXT_FLAG_CAP_CHARACTERS}. -->
            <flag name="textCapCharacters" value="0x00001001" />
            <!-- Can be combined with <var>text</var> and its variations to
                 request capitalization of the first character of every word.  Corresponds to
                 {@link android.text.InputType#TYPE_TEXT_FLAG_CAP_WORDS}. -->
            <flag name="textCapWords" value="0x00002001" />
            <!-- Can be combined with <var>text</var> and its variations to
                 request capitalization of the first character of every sentence.  Corresponds to
                 {@link android.text.InputType#TYPE_TEXT_FLAG_CAP_SENTENCES}. -->
            <flag name="textCapSentences" value="0x00004001" />
            <!-- Can be combined with <var>text</var> and its variations to
                 request auto-correction of text being input.  Corresponds to
                 {@link android.text.InputType#TYPE_TEXT_FLAG_AUTO_CORRECT}. -->
            <flag name="textAutoCorrect" value="0x00008001" />
            <!-- Can be combined with <var>text</var> and its variations to
                 specify that this field will be doing its own auto-completion and
                 talking with the input method appropriately.  Corresponds to
                 {@link android.text.InputType#TYPE_TEXT_FLAG_AUTO_COMPLETE}. -->
            <flag name="textAutoComplete" value="0x00010001" />
            <!-- Can be combined with <var>text</var> and its variations to
                 allow multiple lines of text in the field.  If this flag is not set,
                 the text field will be constrained to a single line.  Corresponds to
                 {@link android.text.InputType#TYPE_TEXT_FLAG_MULTI_LINE}.

                 Note: If this flag is not set and the text field doesn't have max length limit, the
                 framework automatically set maximum length of the characters to 5000 for the
                 performance reasons.
                 -->
            <flag name="textMultiLine" value="0x00020001" />
            <!-- Can be combined with <var>text</var> and its variations to
                 indicate that though the regular text view should not be multiple
                 lines, the IME should provide multiple lines if it can.  Corresponds to
                 {@link android.text.InputType#TYPE_TEXT_FLAG_IME_MULTI_LINE}. -->
            <flag name="textImeMultiLine" value="0x00040001" />
            <!-- Can be combined with <var>text</var> and its variations to
                 indicate that the IME should not show any
                 dictionary-based word suggestions.  Corresponds to
                 {@link android.text.InputType#TYPE_TEXT_FLAG_NO_SUGGESTIONS}. -->
            <flag name="textNoSuggestions" value="0x00080001" />
            <!-- Can be combined with <var>text</var> and its variations to
                 indicate that if there is extra information, the IME should provide
                 {@link android.view.inputmethod.TextAttribute}.  Corresponds to
                 {@link android.text.InputType#TYPE_TEXT_FLAG_ENABLE_TEXT_CONVERSION_SUGGESTIONS}. -->
            <flag name="textEnableTextConversionSuggestions" value="0x00100001" />
            <!-- Text that will be used as a URI.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_TEXT} |
                 {@link android.text.InputType#TYPE_TEXT_VARIATION_URI}. -->
            <flag name="textUri" value="0x00000011" />
            <!-- Text that will be used as an e-mail address.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_TEXT} |
                 {@link android.text.InputType#TYPE_TEXT_VARIATION_EMAIL_ADDRESS}. -->
            <flag name="textEmailAddress" value="0x00000021" />
            <!-- Text that is being supplied as the subject of an e-mail.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_TEXT} |
                 {@link android.text.InputType#TYPE_TEXT_VARIATION_EMAIL_SUBJECT}. -->
            <flag name="textEmailSubject" value="0x00000031" />
            <!-- Text that is the content of a short message.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_TEXT} |
                 {@link android.text.InputType#TYPE_TEXT_VARIATION_SHORT_MESSAGE}. -->
            <flag name="textShortMessage" value="0x00000041" />
            <!-- Text that is the content of a long message.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_TEXT} |
                 {@link android.text.InputType#TYPE_TEXT_VARIATION_LONG_MESSAGE}. -->
            <flag name="textLongMessage" value="0x00000051" />
            <!-- Text that is the name of a person.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_TEXT} |
                 {@link android.text.InputType#TYPE_TEXT_VARIATION_PERSON_NAME}. -->
            <flag name="textPersonName" value="0x00000061" />
            <!-- Text that is being supplied as a postal mailing address.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_TEXT} |
                 {@link android.text.InputType#TYPE_TEXT_VARIATION_POSTAL_ADDRESS}. -->
            <flag name="textPostalAddress" value="0x00000071" />
            <!-- Text that is a password.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_TEXT} |
                 {@link android.text.InputType#TYPE_TEXT_VARIATION_PASSWORD}. -->
            <flag name="textPassword" value="0x00000081" />
            <!-- Text that is a password that should be visible.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_TEXT} |
                 {@link android.text.InputType#TYPE_TEXT_VARIATION_VISIBLE_PASSWORD}. -->
            <flag name="textVisiblePassword" value="0x00000091" />
            <!-- Text that is being supplied as text in a web form.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_TEXT} |
                 {@link android.text.InputType#TYPE_TEXT_VARIATION_WEB_EDIT_TEXT}. -->
            <flag name="textWebEditText" value="0x000000a1" />
            <!-- Text that is filtering some other data.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_TEXT} |
                 {@link android.text.InputType#TYPE_TEXT_VARIATION_FILTER}. -->
            <flag name="textFilter" value="0x000000b1" />
            <!-- Text that is for phonetic pronunciation, such as a phonetic name
                 field in a contact entry.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_TEXT} |
                 {@link android.text.InputType#TYPE_TEXT_VARIATION_PHONETIC}. -->
            <flag name="textPhonetic" value="0x000000c1" />
            <!-- Text that will be used as an e-mail address on a web form.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_TEXT} |
                 {@link android.text.InputType#TYPE_TEXT_VARIATION_WEB_EMAIL_ADDRESS}. -->
            <flag name="textWebEmailAddress" value="0x000000d1" />
            <!-- Text that will be used as a password on a web form.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_TEXT} |
                 {@link android.text.InputType#TYPE_TEXT_VARIATION_WEB_PASSWORD}. -->
            <flag name="textWebPassword" value="0x000000e1" />
            <!-- A numeric only field.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_NUMBER} |
                 {@link android.text.InputType#TYPE_NUMBER_VARIATION_NORMAL}. -->
            <flag name="number" value="0x00000002" />
            <!-- Can be combined with <var>number</var> and its other options to
                 allow a signed number.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_NUMBER} |
                 {@link android.text.InputType#TYPE_NUMBER_FLAG_SIGNED}. -->
            <flag name="numberSigned" value="0x00001002" />
            <!-- Can be combined with <var>number</var> and its other options to
                 allow a decimal (fractional) number.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_NUMBER} |
                 {@link android.text.InputType#TYPE_NUMBER_FLAG_DECIMAL}. -->
            <flag name="numberDecimal" value="0x00002002" />
            <!-- A numeric password field.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_NUMBER} |
                 {@link android.text.InputType#TYPE_NUMBER_VARIATION_PASSWORD}. -->
            <flag name="numberPassword" value="0x00000012" />
            <!-- For entering a phone number.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_PHONE}. -->
            <flag name="phone" value="0x00000003" />
            <!-- For entering a date and time.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_DATETIME} |
                 {@link android.text.InputType#TYPE_DATETIME_VARIATION_NORMAL}. -->
            <flag name="datetime" value="0x00000004" />
            <!-- For entering a date.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_DATETIME} |
                 {@link android.text.InputType#TYPE_DATETIME_VARIATION_DATE}. -->
            <flag name="date" value="0x00000014" />
            <!-- For entering a time.  Corresponds to
                 {@link android.text.InputType#TYPE_CLASS_DATETIME} |
                 {@link android.text.InputType#TYPE_DATETIME_VARIATION_TIME}. -->
            <flag name="time" value="0x00000024" />
        </attr>
        <attr name="maxLength" format="integer" />
        <attr name="progressMinValue" format="integer" />
        <attr name="progressMaxValue" format="integer" />
        <attr name="progressCurrentValue" format="integer" />
        <attr name="thumbDrawable" format="reference" />
        <attr name="progressDrawable" format="reference" />
        <attr name="seekEnable" format="boolean" />
    </declare-styleable>
</resources>