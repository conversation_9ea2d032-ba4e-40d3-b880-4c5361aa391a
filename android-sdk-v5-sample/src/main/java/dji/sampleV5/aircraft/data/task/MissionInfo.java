package dji.sampleV5.aircraft.data.task;

import java.util.List;

/**
 * Describe
 */
public class MissionInfo {

    /**
     * UAVEndTime : 2022-11-17 16:23:10.382
     * UAVFlightMileage : 119.62
     * UAVFlightTime : 58
     * UAVID : UAV_1663577652290
     * UAVRVPath : https://skysys-video-hub.oss-cn-shanghai.aliyuncs.com/record/xl_uav/ch666697682/32_2022-11-17-16-22-12_2022-11-17-16-23-09.mp4
     * UAVStartTime : 2022-11-17 16:22:12.871
     * context :
     * createTime : 2022-11-17 16:21:02
     * endHiveID :
     * endSiteID :
     * finishAction : 1
     * flightRecords : {"list":[{"GPSCount":0,"GPSLevel":0,"UAVPitch":0,"UAVRoll":0,"UAVYaw":0,"altitude":0,"azimuth":92,"distanceStart":0,"downLink":0,"gimbalPitch":0,"gimbalRoll":0,"gimbalYaw":88,"horizontalSpeed":0,"id":0,"latitude":30.86630363,"longitude":121.909567,"subState":200,"timestamp":1668673325000,"upLink":0,"verticalSpeed":0,"zoom":0},{"GPSCount":0,"GPSLevel":0,"UAVPitch":0,"UAVRoll":0,"UAVYaw":0,"altitude":0,"azimuth":92,"distanceStart":0,"downLink":0,"gimbalPitch":0,"gimbalRoll":0,"gimbalYaw":88,"horizontalSpeed":0,"id":0,"latitude":30.86630363,"longitude":121.909567,"subState":200,"timestamp":1668673325000,"upLink":0,"verticalSpeed":0,"zoom":0},{"GPSCount":0,"GPSLevel":0,"UAVPitch":2,"UAVRoll":0,"UAVYaw":0,"altitude":0,"azimuth":93,"distanceStart":0,"downLink":0,"gimbalPitch":0,"gimbalRoll":0,"gimbalYaw":89,"horizontalSpeed":0,"id":0,"latitude":30.86630362,"longitude":121.90956696,"subState":200,"timestamp":1668673321000,"upLink":0,"verticalSpeed":0,"zoom":0}],"total":126}
     * isAutoCP : 0
     * landSpot : {}
     * missionBatch : GF16686732627268523
     * missionEndTime :
     * missionID : DV1668673262756
     * missionName : 大疆机库1028-路点-驭光
     * missioninfo : null
     * operateRecords : {}
     * siteID : SITE1664185010574
     * siteName : 大疆机库测试站点
     * startHiveID : 4TADK8B0000001
     * state : 5
     */

    private String UAVEndTime;
    private double UAVFlightMileage;
    private int UAVFlightTime;
    private String UAVID;
    private String UAVRVPath;
    private String UAVStartTime;
    private String context;
    private String createTime;
    private String endHiveID;
    private String endSiteID;
    private int finishAction;
    private FlightRecordsBean flightRecords;
    private int isAutoCP;
    private String landSpot;
    private String missionBatch;
    private String missionEndTime;
    private String missionID;
    private String missionName;
    private MissionPath missionInfo;
    private MissionPath missioninfo;
    private OperateRecordsBean operateRecords;
    private String siteID;
    private String siteName;
    private String startHiveID;
    private int state;

    public String getUAVEndTime() {
        return UAVEndTime;
    }

    public void setUAVEndTime(String UAVEndTime) {
        this.UAVEndTime = UAVEndTime;
    }

    public double getUAVFlightMileage() {
        return UAVFlightMileage;
    }

    public void setUAVFlightMileage(double UAVFlightMileage) {
        this.UAVFlightMileage = UAVFlightMileage;
    }

    public int getUAVFlightTime() {
        return UAVFlightTime;
    }

    public void setUAVFlightTime(int UAVFlightTime) {
        this.UAVFlightTime = UAVFlightTime;
    }

    public String getUAVID() {
        return UAVID;
    }

    public void setUAVID(String UAVID) {
        this.UAVID = UAVID;
    }

    public String getUAVRVPath() {
        return UAVRVPath;
    }

    public void setUAVRVPath(String UAVRVPath) {
        this.UAVRVPath = UAVRVPath;
    }

    public String getUAVStartTime() {
        return UAVStartTime;
    }

    public void setUAVStartTime(String UAVStartTime) {
        this.UAVStartTime = UAVStartTime;
    }

    public String getContext() {
        return context;
    }

    public void setContext(String context) {
        this.context = context;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getEndHiveID() {
        return endHiveID;
    }

    public void setEndHiveID(String endHiveID) {
        this.endHiveID = endHiveID;
    }

    public String getEndSiteID() {
        return endSiteID;
    }

    public void setEndSiteID(String endSiteID) {
        this.endSiteID = endSiteID;
    }

    public int getFinishAction() {
        return finishAction;
    }

    public void setFinishAction(int finishAction) {
        this.finishAction = finishAction;
    }

    public FlightRecordsBean getFlightRecords() {
        return flightRecords;
    }

    public void setFlightRecords(FlightRecordsBean flightRecords) {
        this.flightRecords = flightRecords;
    }

    public int getIsAutoCP() {
        return isAutoCP;
    }

    public void setIsAutoCP(int isAutoCP) {
        this.isAutoCP = isAutoCP;
    }

    public String getLandSpot() {
        return landSpot;
    }

    public void setLandSpot(String landSpot) {
        this.landSpot = landSpot;
    }

    public String getMissionBatch() {
        return missionBatch;
    }

    public void setMissionBatch(String missionBatch) {
        this.missionBatch = missionBatch;
    }

    public String getMissionEndTime() {
        return missionEndTime;
    }

    public void setMissionEndTime(String missionEndTime) {
        this.missionEndTime = missionEndTime;
    }

    public String getMissionID() {
        return missionID;
    }

    public void setMissionID(String missionID) {
        this.missionID = missionID;
    }

    public String getMissionName() {
        return missionName;
    }

    public void setMissionName(String missionName) {
        this.missionName = missionName;
    }


    public MissionPath getMissionInfo() {
        return missionInfo;
    }

    public void setMissionInfo(MissionPath missionInfo) {
        this.missionInfo = missionInfo;
    }

    public MissionPath getMissioninfo() {
        return missioninfo;
    }

    public void setMissioninfo(MissionPath missioninfo) {
        this.missioninfo = missioninfo;
    }

    public OperateRecordsBean getOperateRecords() {
        return operateRecords;
    }

    public void setOperateRecords(OperateRecordsBean operateRecords) {
        this.operateRecords = operateRecords;
    }

    public String getSiteID() {
        return siteID;
    }

    public void setSiteID(String siteID) {
        this.siteID = siteID;
    }

    public String getSiteName() {
        return siteName;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    public String getStartHiveID() {
        return startHiveID;
    }

    public void setStartHiveID(String startHiveID) {
        this.startHiveID = startHiveID;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public static class MissionPath{
        private FlightParams flightParams;

        public void setFlightParams(FlightParams flightParams) {
            this.flightParams = flightParams;
        }

        public FlightParams getFlightParams() {
            return flightParams;
        }
    }

    public static class FlightParams{
        public List<FlightPath> flightPath;

        public List<Children> childrenList;

        public List<Children> getChildrenList() {
            return childrenList;
        }

        public void setChildrenList(List<Children> childrenList) {
            this.childrenList = childrenList;
        }


        public List<FlightPath> getFlightPath() {
            return flightPath;
        }

        public void setFlightPath(List<FlightPath> flightPath) {
            this.flightPath = flightPath;
        }
    }

    public static class Children{

        private List<FlightPath> flightPath;
        public List<FlightPath> getFlightPath() {
            return flightPath;
        }

        public void setFlightPath(List<FlightPath> flightPath) {
            this.flightPath = flightPath;
        }

    }

    public static class FlightPath{
        private double latitude;
        private double longitude;

        public double getLatitude() {
            return latitude;
        }

        public void setLatitude(double latitude) {
            this.latitude = latitude;
        }

        public double getLongitude() {
            return longitude;
        }

        public void setLongitude(double longitude) {
            this.longitude = longitude;
        }
    }

    public static class FlightRecordsBean {
        public List<FlightRecordBean> list;

        public int toatal;

        public List<FlightRecordBean> getList() {
            return list;
        }

        public void setList(List<FlightRecordBean> list) {
            this.list = list;
        }

        public int getToatal() {
            return toatal;
        }

        public void setToatal(int toatal) {
            this.toatal = toatal;
        }
    }

    public static class FlightRecordBean {

        /**
         * GPSCount : 0
         * GPSLevel : 0
         * UAVPitch : 0
         * UAVRoll : 0
         * UAVYaw : 0
         * altitude : 4.6
         * azimuth : 89
         * distanceStart : 0
         * downLink : 0
         * gimbalPitch : 0
         * gimbalRoll : 0
         * gimbalYaw : 86
         * horizontalSpeed : 0.2
         * id : 0
         * latitude : 30.86630141
         * longitude : 121.90956445
         * subState : 204
         * timestamp : 1668673331000
         * upLink : 0
         * verticalSpeed : 2.8
         * zoom : 0
         */

        private int GPSCount;
        private int GPSLevel;
        private float UAVPitch;
        private float UAVRoll;
        private float UAVYaw;
        private double altitude;
        private int azimuth;
        private int distanceStart;
        private int downLink;
        private float gimbalPitch;
        private float gimbalRoll;
        private float gimbalYaw;
        private double horizontalSpeed;
        private int id;
        private double latitude;
        private double longitude;
        private int subState;
        private long timestamp;
        private int upLink;
        private double verticalSpeed;
        private float zoom;

        public int getGPSCount() {
            return GPSCount;
        }

        public void setGPSCount(int GPSCount) {
            this.GPSCount = GPSCount;
        }

        public int getGPSLevel() {
            return GPSLevel;
        }

        public void setGPSLevel(int GPSLevel) {
            this.GPSLevel = GPSLevel;
        }

        public float getUAVPitch() {
            return UAVPitch;
        }

        public void setUAVPitch(float UAVPitch) {
            this.UAVPitch = UAVPitch;
        }

        public float getUAVRoll() {
            return UAVRoll;
        }

        public void setUAVRoll(float UAVRoll) {
            this.UAVRoll = UAVRoll;
        }

        public float getUAVYaw() {
            return UAVYaw;
        }

        public void setUAVYaw(float UAVYaw) {
            this.UAVYaw = UAVYaw;
        }

        public double getAltitude() {
            return altitude;
        }

        public void setAltitude(double altitude) {
            this.altitude = altitude;
        }

        public int getAzimuth() {
            return azimuth;
        }

        public void setAzimuth(int azimuth) {
            this.azimuth = azimuth;
        }

        public int getDistanceStart() {
            return distanceStart;
        }

        public void setDistanceStart(int distanceStart) {
            this.distanceStart = distanceStart;
        }

        public int getDownLink() {
            return downLink;
        }

        public void setDownLink(int downLink) {
            this.downLink = downLink;
        }

        public float getGimbalPitch() {
            return gimbalPitch;
        }

        public void setGimbalPitch(float gimbalPitch) {
            this.gimbalPitch = gimbalPitch;
        }

        public float getGimbalRoll() {
            return gimbalRoll;
        }

        public void setGimbalRoll(float gimbalRoll) {
            this.gimbalRoll = gimbalRoll;
        }

        public float getGimbalYaw() {
            return gimbalYaw;
        }

        public void setGimbalYaw(float gimbalYaw) {
            this.gimbalYaw = gimbalYaw;
        }

        public double getHorizontalSpeed() {
            return horizontalSpeed;
        }

        public void setHorizontalSpeed(double horizontalSpeed) {
            this.horizontalSpeed = horizontalSpeed;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public double getLatitude() {
            return latitude;
        }

        public void setLatitude(double latitude) {
            this.latitude = latitude;
        }

        public double getLongitude() {
            return longitude;
        }

        public void setLongitude(double longitude) {
            this.longitude = longitude;
        }

        public int getSubState() {
            return subState;
        }

        public void setSubState(int subState) {
            this.subState = subState;
        }

        public long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(long timestamp) {
            this.timestamp = timestamp;
        }

        public int getUpLink() {
            return upLink;
        }

        public void setUpLink(int upLink) {
            this.upLink = upLink;
        }

        public double getVerticalSpeed() {
            return verticalSpeed;
        }

        public void setVerticalSpeed(double verticalSpeed) {
            this.verticalSpeed = verticalSpeed;
        }

        public float getZoom() {
            return zoom;
        }

        public void setZoom(float zoom) {
            this.zoom = zoom;
        }
    }

    public static class OperateRecordsBean{}
}
