package dji.sampleV5.aircraft.manager;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleOwner;

import java.util.HashMap;
import java.util.Map;

import dji.sampleV5.aircraft.base.BaseManager;
import dji.sampleV5.aircraft.camera.CameraController;
import dji.sampleV5.aircraft.databinding.ActivityDefaultLayoutBinding;
import dji.sampleV5.aircraft.ui.AnimationManager;
import dji.sampleV5.aircraft.ui.LayoutManager;

/**
 * 管理器工厂 - 统一创建和管理所有管理器
 */
public class ManagerFactory {
    
    private static volatile ManagerFactory instance;
    private final Map<Class<? extends BaseManager>, BaseManager> managers;
    
    private ManagerFactory() {
        managers = new HashMap<>();
    }
    
    public static ManagerFactory getInstance() {
        if (instance == null) {
            synchronized (ManagerFactory.class) {
                if (instance == null) {
                    instance = new ManagerFactory();
                }
            }
        }
        return instance;
    }
    
    /**
     * 初始化所有管理器
     */
    public void initializeManagers(@NonNull Context context, 
                                  @NonNull ActivityDefaultLayoutBinding binding,
                                  @NonNull LifecycleOwner lifecycleOwner) {
        
        // 创建布局管理器
        LayoutManager layoutManager = new LayoutManager(context, binding);
        layoutManager.initialize();
        lifecycleOwner.getLifecycle().addObserver(layoutManager);
        managers.put(LayoutManager.class, layoutManager);
        
        // 创建动画管理器
        AnimationManager animationManager = new AnimationManager(context, binding, layoutManager);
        animationManager.initialize();
        lifecycleOwner.getLifecycle().addObserver(animationManager);
        managers.put(AnimationManager.class, animationManager);
        
        // 创建相机控制器
        CameraController cameraController = new CameraController(context, binding);
        cameraController.initialize();
        lifecycleOwner.getLifecycle().addObserver(cameraController);
        managers.put(CameraController.class, cameraController);
        
        // TODO: 添加其他管理器
        // MissionController, MapController, etc.
    }
    
    /**
     * 获取指定类型的管理器
     */
    @SuppressWarnings("unchecked")
    public <T extends BaseManager> T getManager(@NonNull Class<T> managerClass) {
        return (T) managers.get(managerClass);
    }
    
    /**
     * 检查管理器是否存在
     */
    public boolean hasManager(@NonNull Class<? extends BaseManager> managerClass) {
        return managers.containsKey(managerClass);
    }
    
    /**
     * 销毁所有管理器
     */
    public void destroyAllManagers() {
        for (BaseManager manager : managers.values()) {
            if (manager != null && manager.isInitialized()) {
                manager.destroy();
            }
        }
        managers.clear();
    }
    
    /**
     * 销毁指定管理器
     */
    public void destroyManager(@NonNull Class<? extends BaseManager> managerClass) {
        BaseManager manager = managers.remove(managerClass);
        if (manager != null && manager.isInitialized()) {
            manager.destroy();
        }
    }
    
    /**
     * 获取所有管理器的状态信息
     */
    public String getManagersStatus() {
        StringBuilder status = new StringBuilder();
        status.append("Managers Status:\n");
        
        for (Map.Entry<Class<? extends BaseManager>, BaseManager> entry : managers.entrySet()) {
            String className = entry.getKey().getSimpleName();
            boolean isInitialized = entry.getValue() != null && entry.getValue().isInitialized();
            status.append("- ").append(className).append(": ")
                  .append(isInitialized ? "Initialized" : "Not Initialized").append("\n");
        }
        
        return status.toString();
    }
    
    /**
     * 重新初始化指定管理器
     */
    public <T extends BaseManager> void reinitializeManager(@NonNull Class<T> managerClass) {
        T manager = getManager(managerClass);
        if (manager != null) {
            if (manager.isInitialized()) {
                manager.destroy();
            }
            manager.initialize();
        }
    }
}
