package dji.sampleV5.aircraft.kmz.model.enums;

import java.util.Objects;

/**
 * @author: wang<PERSON>i
 * @date: 2022/8/4 15:51
 * @description: 动作类型
 */
public enum ActionTypeEnum {

    /**
     * 悬停等待
     */
    HOVER(1, "hover"),

    /**
     * 开始录像
     */
    START_RECORD(2, "startRecord"),

    /**
     * 结束录像
     */
    STOP_RECORD(3, "stopRecord"),

    /**
     * 旋转云台
     */
    GIMBAL_ROTATE(4, "gimbalRotate"),

    /**
     * 单拍
     */
    TAKE_PHOTO(5, "takePhoto"),

    /**
     * 飞行器偏航
     */
    ROTATE_YAW(6, "rotateYaw"),

    /**
     * 变焦
     */
    ZOOM(7, "zoom"),

    /**
     * 等时拍摄
     */
    MULTIPLE_TIMEING(10, "multipleTiming"),

    /**
     * 等距拍摄
     */
    MULTIPLE_DISTANCE(11, "multipleDistance"),

    /**
     * 停止间隔拍照
     */
    STOP_TAKE_PHOTO(12, "stopTakePhoto"),

    /**
     * 全景图拍摄
     */
    PANO_SHOT(20, "panoShot");

    private final Integer type;
    private final String action;

    ActionTypeEnum(Integer type, String action) {
        this.type = type;
        this.action = action;
    }

    public String getAction() {
        return this.action;
    }

    public Integer getType() {
        return this.type;
    }

    /**
     * 通过动作取类型
     *
     * @param action 动作
     * @return 类型数字
     */
    public static Integer getTypeByAction(String action) {
        for (ActionTypeEnum actionType : ActionTypeEnum.values()) {
            if (actionType.action.equals(action)) {
                return actionType.type;
            }
        }
        return null;
    }

    /**
     * 通过类型取动作
     *
     * @param type 类型
     * @return 动作字符串
     */
    public static String getActionByType(Integer type) {
        for (ActionTypeEnum actionType : ActionTypeEnum.values()) {
            if (actionType.type.equals(type)) {
                return actionType.action;
            }
        }
        return null;
    }

    public static ActionTypeEnum getActionTypeByType(Integer type) {
        if (Objects.isNull(type)) {
            return null;
        }

        for (ActionTypeEnum actionTypeEnum : ActionTypeEnum.values()) {
            if (Objects.equals(actionTypeEnum.getType(), type)) {
                return actionTypeEnum;
            }
        }
        return null;
    }
}
