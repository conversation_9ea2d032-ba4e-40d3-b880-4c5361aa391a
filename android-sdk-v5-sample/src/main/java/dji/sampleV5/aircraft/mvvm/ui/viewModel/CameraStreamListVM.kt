package dji.sampleV5.aircraft.mvvm.ui.viewModel

import android.view.Surface
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import dji.sdk.keyvalue.value.common.ComponentIndexType
import dji.v5.manager.KeyManager
import dji.v5.manager.datacenter.MediaDataCenter
import dji.v5.manager.interfaces.ICameraStreamManager.AvailableCameraUpdatedListener

/**
 * Copyright (C), 2015-2024
 * FileName: CameraStreamListVM
 * Author: 80945
 * Date: 2024/12/3 15:55
 * Description: dji视频流
 */
class CameraStreamListVM: ViewModel(), AvailableCameraUpdatedListener {
    private val _availableCameraListData = MutableLiveData<List<ComponentIndexType>>(ArrayList())
    init {
        MediaDataCenter.getInstance().cameraStreamManager.addAvailableCameraUpdatedListener(this)
    }

    override fun onCleared() {
        super.onCleared()
        MediaDataCenter.getInstance().cameraStreamManager.removeAvailableCameraUpdatedListener(this)
    }

    val availableCameraListData: LiveData<List<ComponentIndexType>>
        get() = _availableCameraListData

    override fun onAvailableCameraUpdated(availableCameraList: MutableList<ComponentIndexType>) {
        _availableCameraListData.postValue(availableCameraList)
    }

    override fun onCameraStreamEnableUpdate(cameraStreamEnableMap: MutableMap<ComponentIndexType, Boolean>) {}
}