<!--
  ~ Copyright (c) 2018-2020 DJI
  ~
  ~ Permission is hereby granted, free of charge, to any person obtaining a copy
  ~ of this software and associated documentation files (the "Software"), to deal
  ~ in the Software without restriction, including without limitation the rights
  ~ to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
  ~ copies of the Software, and to permit persons to whom the Software is
  ~ furnished to do so, subject to the following conditions:
  ~
  ~ The above copyright notice and this permission notice shall be included in all
  ~ copies or substantial portions of the Software.
  ~
  ~ THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  ~ IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
  ~ FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
  ~ AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
  ~ LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
  ~ OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
  ~ SOFTWARE.
  ~
  -->

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:width="347dp"
    android:height="35dp"
    android:viewportWidth="347"
    android:viewportHeight="35"
    tools:ignore="VectorRaster">
  <path
      android:pathData="M340.51,21.382L346.767,16.463C319.001,10.512 288.104,6.109 255.511,3.362L252.446,9.124C283.822,11.695 313.607,15.814 340.51,21.382"
      android:strokeWidth="1"
      android:fillColor="#4BD663"
      android:fillAlpha="0.4"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M332.163,27.651L338.559,22.624C311.978,17.172 282.596,13.135 251.658,10.606L248.538,16.472C278.234,18.824 306.48,22.579 332.163,27.651"
      android:strokeWidth="1"
      android:fillColor="#4BD663"
      android:fillAlpha="0.5"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
</vector>
