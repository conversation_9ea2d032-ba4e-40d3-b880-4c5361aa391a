# PSDK日志问题排查指南

## 问题现象
**描述**: 日志信息进入当前类中后，看不到打印的日志

## 根本原因分析

### 1. BuildConfig.DEBUG引用错误
**问题**: 代码中错误引用了XLog库的BuildConfig，而不是应用的BuildConfig
**影响**: 导致DEBUG模式判断错误，日志等级设置不正确

**修复前**:
```java
// MApplication.java
import com.elvishew.xlog.BuildConfig;  // ❌ 错误引用

// PSDKLogger.java  
import com.elvishew.xlog.BuildConfig;  // ❌ 错误引用
```

**修复后**:
```java
// MApplication.java
// 移除错误的import，直接使用应用BuildConfig

// PSDKLogger.java
import com.skysys.fly.BuildConfig;  // ✅ 正确引用
```

### 2. XLog初始化配置问题
**问题**: XLog配置不完整，缺少控制台输出
**影响**: 日志只输出到文件，不在Logcat中显示

**修复前**:
```java
XLog.init(BuildConfig.DEBUG ? LogLevel.ALL : LogLevel.NONE);  // 使用错误的BuildConfig
XLog.init(config, filePrinter);  // 只有文件输出
```

**修复后**:
```java
LogConfiguration config = new LogConfiguration.Builder()
    .logLevel(com.skysys.fly.BuildConfig.DEBUG ? LogLevel.ALL : LogLevel.NONE)
    .tag("UAV_LOG")
    .build();

Printer androidPrinter = new AndroidPrinter();  // 控制台输出
Printer filePrinter = new FilePrinter...;       // 文件输出

XLog.init(config, androidPrinter, filePrinter);  // 同时支持控制台和文件
```

### 3. 日志等级配置问题
**问题**: 日志等级设置过高，导致部分日志不显示
**影响**: DEBUG和INFO级别的日志被过滤

## 解决方案

### 方案1: 修复XLog配置 (已实施)

#### 1.1 修复BuildConfig引用
- ✅ MApplication.java: 移除错误的BuildConfig导入
- ✅ PSDKLogger.java: 使用正确的应用BuildConfig

#### 1.2 完善XLog初始化
- ✅ 添加AndroidPrinter支持控制台输出
- ✅ 使用正确的BuildConfig判断DEBUG模式
- ✅ 统一日志配置和标签

#### 1.3 添加备用日志输出
- ✅ 在PSDKLogger中添加Android Log备用输出
- ✅ 确保即使XLog配置有问题，也能看到日志

### 方案2: 强制启用调试日志

#### 2.1 强制调试开关
```java
private static final boolean FORCE_DEBUG = true;  // 强制启用
private static final boolean DEBUG_ENABLED = BuildConfig.DEBUG || FORCE_DEBUG;
```

#### 2.2 备用日志系统
```java
private static final boolean USE_ANDROID_LOG_BACKUP = true;

private static void logToAndroid(String tag, String level, String message) {
    if (USE_ANDROID_LOG_BACKUP) {
        switch (level) {
            case "DEBUG": Log.d(tag, message); break;
            case "INFO":  Log.i(tag, message); break;
            case "WARN":  Log.w(tag, message); break;
            case "ERROR": Log.e(tag, message); break;
        }
    }
}
```

## 验证方法

### 1. 日志测试功能
在代码中添加了测试方法：
```java
PSDKLogger.testLogOutput();  // 测试所有日志类型
```

### 2. Logcat过滤器设置
在Android Studio的Logcat中使用以下过滤器：
```
tag:PSDK_PAYLOAD OR tag:PSDK_CONNECTION OR tag:PSDK_DATA_RECEIVE OR tag:PSDK_DATA_PARSE OR tag:PSDK_DATA_STORE OR tag:PSDK_ERROR OR tag:UAV_LOG
```

### 3. 检查日志配置
调用配置检查方法：
```java
String config = PSDKLogger.getLogConfig();
Log.i("PSDK_CONFIG", config);
```

## 预期日志输出

### 测试日志示例
```
I/PSDK_PAYLOAD: === PSDK Logger Test Start ===
I/PSDK_CONNECTION: [TEST_CONNECTION] - SUCCESS | 测试连接日志输出
D/PSDK_DATA_RECEIVE: [DATA_RECEIVE] - INFO | 接收数据 | Length: 4, Data: 1A 2B 3C 4D
D/PSDK_DATA_PARSE: [TEST_DATA] - SUCCESS | Value: 123.45 | 测试数据解析日志
I/PSDK_DATA_STORE: [TEST_STORE] - SUCCESS | 测试数据存储日志
E/PSDK_ERROR: [TEST_ERROR] - ERROR | RuntimeException: 测试错误日志
I/PSDK_PAYLOAD: === PSDK Logger Test End ===
```

### 实际PSDK日志示例
```
I/PSDK_CONNECTION: [GET_PAYLOAD] - SUCCESS | Payload对象获取成功
I/PSDK_CONNECTION: [SET_CALLBACK] - SUCCESS | CommandDataCallback设置成功
D/PSDK_DATA_RECEIVE: [DATA_RECEIVE] - INFO | 接收数据 | Length: 48, Data: 1A 2B 3C...
D/PSDK_DATA_PARSE: [DATA_CATEGORY] - SUCCESS | Value: 48.00 | 数据长度>=48，处理湿度和压力
D/PSDK_DATA_PARSE: [CO2] - SUCCESS | Value: 425.60 | CO2数据解析成功
I/PSDK_DATA_STORE: [UPDATE_PAYLOAD_DATA] - SUCCESS | 数据包存储成功
```

## 常见问题排查

### Q1: 仍然看不到日志
**检查项**:
1. 确认应用运行在DEBUG模式
2. 检查Logcat过滤器设置
3. 确认`FORCE_DEBUG = true`
4. 查看是否有异常阻止日志输出

### Q2: 只看到部分日志
**检查项**:
1. 确认日志等级设置
2. 检查XLog初始化是否成功
3. 查看是否有异常被捕获但未处理

### Q3: 日志格式不正确
**检查项**:
1. 确认使用了正确的PSDKLogger方法
2. 检查日志标签是否正确
3. 验证日志消息格式

## 调试建议

### 1. 逐步验证
1. 先运行`PSDKLogger.testLogOutput()`
2. 确认测试日志正常显示
3. 再检查实际PSDK日志

### 2. 分级调试
1. 先确认ERROR级别日志显示
2. 再确认WARN级别日志显示
3. 最后确认DEBUG级别日志显示

### 3. 备用方案
如果XLog仍有问题，可以临时使用纯Android Log：
```java
Log.d("PSDK_DEBUG", "直接使用Android Log输出");
```

## 后续优化建议

### 1. 日志配置管理
- 考虑使用配置文件管理日志等级
- 添加运行时动态调整日志等级的功能

### 2. 性能优化
- 监控日志输出对性能的影响
- 考虑异步日志输出

### 3. 日志分析
- 集成日志分析工具
- 添加日志统计和报告功能

---

**文档状态**: ✅ 问题分析完成
**修复状态**: ✅ 代码修复完成
**验证方法**: ✅ 测试功能已添加
**下一步**: 运行应用验证日志输出
