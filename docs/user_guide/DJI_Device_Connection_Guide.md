# DJI无人机连接指南

## 问题现象
- API Key注册成功
- 但显示"DJI USB设备检测: 未检测到"
- 无法连接无人机

## 解决方案

### 🔧 **标准连接流程（必须按顺序执行）**

#### 第一步：设备准备
1. **确保设备电量充足**
   - 无人机电量 > 50%
   - 遥控器电量 > 50%
   - 手机电量 > 30%

2. **检查设备状态**
   - 无人机无故障提示
   - 遥控器功能正常
   - USB线质量良好（建议使用原装线）

#### 第二步：正确的开机顺序
```
1. 先开启遥控器 → 等待完全启动
2. 再开启无人机 → 等待自检完成
3. 等待遥控器显示连接成功（通常有声音提示）
4. 确认遥控器屏幕显示无人机已连接
```

#### 第三步：USB连接
1. **使用质量良好的USB线**
   - 建议使用原装USB线
   - 确保线材支持数据传输（不是仅充电线）

2. **连接手机**
   - 将USB线连接遥控器的USB端口
   - 连接到手机的USB端口
   - 确保连接牢固

#### 第四步：启动应用
1. **启动应用前确认**
   - 遥控器已连接无人机
   - USB线已连接手机
   - 手机已识别USB设备

2. **启动应用**
   - 打开DJI应用
   - 等待SDK初始化完成
   - 查看连接状态

### 🔍 **故障排除**

#### 问题1：未检测到任何USB设备
**可能原因**：
- USB线质量问题
- 手机USB端口问题
- 遥控器USB端口问题

**解决方案**：
- 更换USB线
- 尝试不同的USB端口
- 重新插拔USB连接

#### 问题2：检测到USB设备但不是DJI设备
**可能原因**：
- 遥控器未正确连接无人机
- 遥控器固件版本问题
- USB模式设置错误

**解决方案**：
- 重新配对遥控器和无人机
- 检查遥控器USB模式设置
- 更新遥控器固件

#### 问题3：检测到DJI设备但权限被拒绝
**可能原因**：
- USB权限未授予
- 系统安全设置阻止

**解决方案**：
- 重新授予USB权限
- 检查系统安全设置
- 重启应用

### 📱 **不同设备型号的特殊说明**

#### DJI Mini系列
- 确保遥控器处于"手机连接模式"
- 某些型号需要在遥控器设置中启用"USB调试"

#### DJI Air系列
- 确保使用正确的USB端口（通常是底部端口）
- 检查遥控器屏幕是否显示"设备已连接"

#### DJI Mavic系列
- 某些型号需要在DJI GO应用中先连接一次
- 确保遥控器固件版本与无人机匹配

### 🔧 **高级故障排除**

#### 方法1：重置连接
```
1. 关闭应用
2. 断开USB连接
3. 关闭无人机和遥控器
4. 等待30秒
5. 按标准流程重新连接
```

#### 方法2：清除应用数据
```
1. 进入手机设置 → 应用管理
2. 找到DJI应用
3. 清除应用数据和缓存
4. 重新启动应用
5. 重新进行连接
```

#### 方法3：检查系统兼容性
- 确认Android版本兼容性
- 检查是否有系统更新
- 确认手机型号支持USB OTG

### 📋 **连接成功的标志**

#### 应用日志中应该看到：
```
=== USB设备扫描 ===
检测到 1 个USB设备:
- 制造商: DJI, 型号: [设备型号], 版本: [版本号]
DJI USB设备检测: [设备型号]
USB权限状态: true
startConnectionToProduct result: true
```

#### 应用界面应该显示：
- 无人机型号名称
- 连接状态为"已连接"
- 电池电量信息
- GPS信号状态

### ⚠️ **重要提醒**

1. **连接顺序很重要**：必须先开遥控器，再开无人机，最后连USB
2. **等待时间**：每个步骤之间要有足够的等待时间
3. **USB线质量**：劣质USB线是连接失败的常见原因
4. **权限授予**：首次连接时必须授予USB权限
5. **固件版本**：确保无人机和遥控器固件版本匹配

### 📞 **如果仍然无法连接**

请提供以下信息以便进一步诊断：
1. 无人机型号
2. 遥控器型号
3. 手机型号和Android版本
4. 完整的应用日志
5. USB设备扫描结果
