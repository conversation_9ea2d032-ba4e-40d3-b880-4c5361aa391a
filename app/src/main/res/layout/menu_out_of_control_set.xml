<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">


    <RelativeLayout
        android:id="@+id/rl_out_of_control"
        style="@style/AircraftSetPadding"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/space_5"
        android:paddingBottom="@dimen/space_5">


        <TextView
            style="@style/AircraftStandardTextView"
            android:layout_centerVertical="true"
            android:text="@string/out_of_control" />

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:padding="@dimen/space_10">

            <TextView
                android:id="@+id/tv_out_of_control"
                android:layout_width="@dimen/space_110"
                android:layout_height="wrap_content"
                android:background="@drawable/edittext"
                android:gravity="center_horizontal"
                android:padding="@dimen/space_3"
                android:text=""
                android:textColor="@color/colorBright"
                android:textSize="@dimen/text_size_14" />

            <ImageButton
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignTop="@id/tv_out_of_control"
                android:layout_alignEnd="@id/tv_out_of_control"
                android:layout_alignBottom="@id/tv_out_of_control"
                android:background="@null"
                android:src="@drawable/down_arrow"
                tools:ignore="ContentDescription" />
        </RelativeLayout>
    </RelativeLayout>
</layout>