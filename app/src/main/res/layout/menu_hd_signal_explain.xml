<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:orientation="horizontal">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="l"
        android:textColor="@color/green"
        android:textSize="@dimen/text_size_17"
        android:textStyle="bold"
        tools:ignore="HardcodedText" />

    <TextView
        style="@style/AircraftStandardTextView"
        android:text="@string/signal" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="l"
        android:textColor="@color/red"
        android:textSize="@dimen/text_size_17"
        android:textStyle="bold"
        tools:ignore="HardcodedText" />

    <TextView
        style="@style/AircraftStandardTextView"
        android:text="@string/signal1" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="l"
        android:textColor="@color/blue"
        android:textSize="@dimen/text_size_17"
        android:textStyle="bold"
        tools:ignore="HardcodedText" />

    <TextView
        style="@style/AircraftStandardTextView"
        android:text="@string/signal2" />
</LinearLayout>
