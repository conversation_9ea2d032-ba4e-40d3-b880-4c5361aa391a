<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <data>

        <import type="android.view.View" />

        <variable
            name="activityMenuPresenter"
            type="com.skysys.fly.page.fly.setting.ActivityMenuPresenter" />
    </data>

    <RelativeLayout
        android:id="@+id/rl_HD_EXT_OPEN"
        style="@style/AircraftSetPadding"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="@{activityMenuPresenter.HDMISupportEXT?View.VISIBLE:View.GONE}">

        <include layout="@layout/line_between" />

        <TextView
            style="@style/AircraftStandardTextView"
            android:layout_centerVertical="true"
            android:text="@string/he_ext_open" />

        <ToggleButton
            android:id="@+id/toggle_HD_EXT_OPEN"
            android:layout_width="36dp"
            android:layout_height="32dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="@dimen/space_20"
            android:background="@drawable/toggle_button1"
            android:textOff=""
            android:textOn=""/>
    </RelativeLayout>
</layout>