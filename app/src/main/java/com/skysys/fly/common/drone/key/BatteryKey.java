package com.skysys.fly.common.drone.key;


public class <PERSON><PERSON><PERSON> extends BaseKey {
    public static final String FULL_CHARGE_CAPACITY = "FullChargeCapacity";
    public static final String CHARGE_REMAINING = "ChargeRemaining";
    public static final String VOLTAGE = "Voltage";
    public static final String CURRENT = "Current";
    public static final String LIFETIME_REMAINING = "LifetimeRemaining";
    public static final String IS_BIG_BATTERY = "IsBigBattery";
    public static final String CHARGE_REMAINING_IN_PERCENT = "ChargeRemainingInPercent";
    public static final String TEMPERATURE = "Temperature";
    public static final String NUMBER_OF_DISCHARGES = "NumberOfDischarges";
    public static final String LATEST_WARNING_RECORD = "LatestWarningRecord";
    public static final String HISTORICAL_WARNING_RECORDS = "HistoricalWarningRecords";
    public static final String SELF_DISCHARGE_IN_DAYS = "SelfDischargeInDays";
    public static final String SERIAL_NUMBER = "SerialNumber";
    public static final String CELL_VOLTAGES = "CellVoltages";
    public static final String NUMBER_OF_CELLS = "NumberOfCells";
    public static final String IS_SMART_BATTERY = "isSmartBattery";
    public static final String IS_IN_SINGLE_BATTERY_MODE = "isInSingleBatteryMode";
    public static final String OVERVIEWS = "Overviews";
    public static final String HIGHEST_TEMPERATURE = "HighestTemperature";
    public static final String IS_ANY_BATTERY_DISCONNECTED = "IsAnyBatteryDisconnected";
    public static final String IS_VOLTAGE_DIFFERENCE_DETECTED = "IsVoltageDifferenceDetected";
    public static final String IS_LOW_CELL_VOLTAGE_DETECTED = "IsLowCellVoltageDetected";
    public static final String IS_CELL_DAMAGED = "IsCellDamaged";
    public static final String IS_FIRMWARE_DIFFERENCE_DETECTED = "IsFirmwareDifferenceDetected";
    public static final String NUMBER_OF_CONNECTED_BATTERIES = "NumberOfConnectedBatteries";
    public static final String LEVEL_1_CELL_VOLTAGE_THRESHOLD = "Level1CellVoltageThreshold";
    public static final String LEVEL_2_CELL_VOLTAGE_THRESHOLD = "Level2CellVoltageThreshold";
    public static final String LEVEL_1_CELL_VOLTAGE_BEHAVIOR = "Level1CellVoltageBehavior";
    public static final String LEVEL_2_CELL_VOLTAGE_BEHAVIOR = "Level2CellVoltageBehavior";
    public static final String IS_BEING_CHARGED = "IsBeingCharged";
    public static final String IS_BATTERY_SELF_HEATING = "IsBatterySelfHeating";
    public static final String FIRMWARE_VERSION = "FirmwareVersion";
    public static final String CONNECTION_STATE = "ConnectionState";
    public static final String PAIR_BATTERIES = "PairBatteries";
    public static final String PAIRING_STATE = "PairingState";
    public static final String CELL_VOLTAGE_LEVEL = "CellVoltageLevel";
    public static final String AGGREGATION_STATE = "AggregationState";

    public int index;

    protected BatteryKey(String key, int category) {
        super(key, category);
    }

    private BatteryKey(String key, int index, int category) {
        this(key, category);
        this.index = index;
    }

    public static BatteryKey create(String key) {
        return new BatteryKey(key, KeyCategory.BATTERY);
    }

    public static BatteryKey create(String key, int index) {
        return new BatteryKey(key, index, KeyCategory.BATTERY_MULTI);
    }
}
