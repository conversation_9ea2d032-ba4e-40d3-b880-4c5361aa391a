package com.skysys.fly.common.mqtt;

import static org.eclipse.paho.client.mqttv3.MqttConnectOptions.MQTT_VERSION_3_1_1;

import android.content.Context;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;

import com.google.gson.Gson;
import com.skysys.fly.MApplication;
import com.skysys.fly.common.ContextUtil;
import com.skysys.fly.common.drone.DJIHelper;
import com.skysys.fly.common.drone.key.FlightControllerKey;
import com.skysys.fly.common.drone.key.GimbalKey;
import com.skysys.fly.common.drone.key.KeyManager;
import com.skysys.fly.common.lbs.bean.AppLatLng;
import com.skysys.fly.common.listener.YNListener0;
import com.skysys.fly.data.mission.MissionStatus;
import com.skysys.fly.data.mission.PayloadData;
import com.skysys.fly.data.preference.SpUtil;
import com.skysys.fly.net.bean.AirData;
import com.skysys.fly.net.bean.BatteryInfo;
import com.skysys.fly.net.bean.GasDataInfo;
import com.skysys.fly.net.bean.MissionJson;
import com.skysys.fly.net.bean.MqttBean;
import com.skysys.fly.net.bean.MqttInfo;
import com.skysys.fly.net.bean.UAVInfoSN;
import com.skysys.fly.page.home.HomeActivity;
import com.skysys.fly.util.FormatUtil;
import com.skysys.fly.util.ToastUtil;
import com.skysys.fly.util.coordinate.GeoSysConversion;

import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.MqttCallbackExtended;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;

import java.io.IOException;
import java.nio.charset.Charset;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.atomic.AtomicBoolean;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import dji.common.battery.AggregationState;
import dji.common.battery.BatteryOverview;
import dji.common.battery.BatteryState;
import dji.common.error.DJIError;
import dji.common.flightcontroller.WindDirection;
import dji.common.remotecontroller.SecondaryVideoDisplayMode;
import dji.common.util.CommonCallbacks;
import dji.sdk.battery.Battery;
import dji.sdk.sdkmanager.DJISDKManager;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

public class MQtt {
    private static final String TAG = "MQtt";
    private static final String CONNECT_ERROR_AUTHENTICATION = "无权连接";

    private static final int DISCONNECT_TIME_OUT = 5;
    private final AtomicBoolean authenticationLock = new AtomicBoolean(false);

    private Context context;

    private boolean connected = false;

    private MQttListener mqttListener;

    private String serverUrl = "tcp://api.skysys.cn:11883";
    private String userName = "LTAI4G93t1z9tXkGY6BJrkCk";
    /*private String serverUrl = "tcp://mqtt-cn-0pp0s45s703.mqtt.aliyuncs.com:1883";
    private String userName = "LTAI4G93t1z9tXkGY6BJrkCk";

    private String serverUrl_local = "tcp://api.skysys.cn:21883";
    private String userName_local = "admin";*/
    private char[] passWord;
    private String topicPublish = "toConsoleDebug";
    private String[] topicFilters = new String[]{"toUAV/p2p"};
    private String clientIdFinal;

    private MemoryPersistence mPersistence;
    private MqttConnectOptions options;
    private MqttClient mqttClient;
    final int[] qos = {0};
    private Timer timer_mqtt;
    private TimerTask task_mqtt;
    private String sn;
    private int batteryPercentage;
    private List<Battery> batteries;
    private List<BatteryInfo> batteryInfos = new ArrayList<>();

    public MQtt(Context context) {
        this.context = context;
    }

    public void setMQttListener(MQttListener listener) {
        this.mqttListener = listener;
    }

    public void init(String sn) {
        if (sn == null) {
            return;
        }
        this.sn = sn;
        ContextUtil.getHandler().postDelayed(getBatteriesInfoRunnable, 2000);
        connect(sn);
    }

    public void connect(String clientId) {
        if (!connected) {
            ToastUtil.show("开始尝试连接MQTT");
            try {
                String beizhu = SpUtil.getBeiZhu();
                if (TextUtils.isEmpty(beizhu)) {
                    topicPublish = "device/uav/state/" + sn;
                    topicFilters[0] = "toUAV/p2p/" + sn;
                } else {
                    topicPublish = "device/uav/state/" + beizhu;
                    topicFilters[0] = "toUAV/p2p/" + beizhu;
                }

                clientIdFinal = "GID_UAV@@@" + clientId;
                //passWord = macSignature("GID_UAV", "DwyfjGIBBAtqt34IF9CPLxoEqyC9cI").toCharArray();
                //passWord = "public".toCharArray();
                mPersistence = new MemoryPersistence();
                options = new MqttConnectOptions();
                serverUrl = SpUtil.getIP();
                options.setServerURIs(new String[]{serverUrl});
               /* options.setUserName(userName);
                options.setPassword(passWord);*/
                options.setCleanSession(true);
                options.setMqttVersion(MQTT_VERSION_3_1_1);
                options.setAutomaticReconnect(true);
                options.setConnectionTimeout(5);
                options.setKeepAliveInterval(10);
                mqttClient = new MqttClient(serverUrl, clientIdFinal, mPersistence);
                mqttClient.setCallback(new MyMQTTCallback());
                mqttClient.connect(options);
            } catch (Exception e) {
                ToastUtil.show("connect error:" + e.getLocalizedMessage());
                Log.e("mqtt", "connect error: " + e.getLocalizedMessage());
            }
        }
    }

    public String macSignature(String text, String secretKey) throws InvalidKeyException, NoSuchAlgorithmException {
        Charset charset = Charset.forName("UTF-8");
        String algorithm = "HmacSHA1";
        Mac mac = Mac.getInstance(algorithm);
        mac.init(new SecretKeySpec(secretKey.getBytes(charset), algorithm));
        byte[] bytes = mac.doFinal(text.getBytes(charset));
        return new String(Base64.encode(bytes, Base64.NO_WRAP), charset);
        //return new String(Base64.encodeBase64(bytes), charset);
    }

    class MyMQTTCallback implements MqttCallbackExtended {
        @Override
        public void connectComplete(boolean reconnect, String serverURI) {
            Log.e("mqtt", "MQTT connectComplete: " + serverURI + "  reconnect = " + reconnect);
            ToastUtil.show("MQTT 已连接");
            //app.getInfo().isMqttConnected = true;
            connected = true;

            if (mqttListener != null) {
                mqttListener.onConnected();
            }
            try {
                mqttClient.subscribe(topicFilters, qos);
            } catch (Exception e) {
                Log.e("mqtt", "MQTT 订阅报错: " + e.getLocalizedMessage());
            }

            if (!reconnect) {
                sendStatusDebug();
            }
        }

        @Override
        public void connectionLost(Throwable cause) {
            ToastUtil.show("MQTT 断开连接");
            Log.e("mqtt", "connectionLost: " + cause.getLocalizedMessage());
            if (mqttListener != null) {
                mqttListener.onDisconnected();
            }
            //app.getInfo().isMqttConnected = false;
        }

        @Override
        public void messageArrived(String topic, final MqttMessage message) throws Exception {
            Log.e("mqtt", "messageArrived: tpoic = " + topic + "  message=" + message.toString());
            if (mqttListener != null) {
                mqttListener.onGetMsg(topic, message.toString());
                //mqtt.acknowledgeMessage(((ParcelableMqttMessage) message).getMessageId());
            }
        }

        @Override
        public void deliveryComplete(final IMqttDeliveryToken token) {
            if (mqttListener != null) {
                mqttListener.onMsgDelivered();
            }

        }
    }

    public void reset() {
        //Log.e(TAG,"MQtt#reset");
        if (mqttListener != null) {
            mqttListener.onPreRelease();
        }
        try {
            if (timer_mqtt != null) {
                timer_mqtt.cancel();
                timer_mqtt.purge();
                timer_mqtt = null;
            }
        } catch (Exception e) {
            Log.e(TAG, e.getMessage());
        }
        disconnect();
        connected = false;
        if (mqttListener != null) {
            mqttListener.onRelease();
        }
    }

    public boolean isConnected() {
        return this.connected;
    }

    public void sendStatusDebug() {
        timer_mqtt = new Timer();
        task_mqtt = new MQTTTimeTask();
        timer_mqtt.schedule(task_mqtt, 1500, 500);
    }

    MqttBean mqttBean = new MqttBean();

    class MQTTTimeTask extends TimerTask {
        @Override
        public void run() {
            if (mqttClient == null || !mqttClient.isConnected() || !DJIHelper.getInstance().isAircraftConnected()) {
                return;
            }
            //121.227815,31.779967 临港坐标
            boolean isAircraftOnline = true;
            boolean isOSDKAvailable = false;

            double lat84 = 0;
            double lon84 = 0;
            double lat02 = 0;
            double lon02 = 0;
            double lat84home = 0;
            double lon84home = 0;
            double lat02home = 0;
            double lon02home = 0;


            if (DJIHelper.getInstance().isAircraftConnected()) {
                Double aircraftLat = (Double) KeyManager.getInstance().getValue(FlightControllerKey.create(FlightControllerKey.AIRCRAFT_LOCATION_LATITUDE));
                Double aircraftLng = (Double) KeyManager.getInstance().getValue(FlightControllerKey.create(FlightControllerKey.AIRCRAFT_LOCATION_LONGITUDE));
                if (aircraftLat != null && !Double.isNaN(aircraftLat)) {
                    lat84 = aircraftLat;
                    lon84 = aircraftLng;
                    double[] appLatLng02 = GeoSysConversion.wgs84toGCJ02(lat84, lon84);
                    lat02 = appLatLng02[0];
                    lon02 = appLatLng02[1];
                }


                Double aircraftLatHome = (Double) KeyManager.getInstance().getValue(FlightControllerKey.create(FlightControllerKey.HOME_LOCATION_LATITUDE));
                Double aircraftLngHome = (Double) KeyManager.getInstance().getValue(FlightControllerKey.create(FlightControllerKey.HOME_LOCATION_LONGITUDE));
                if (aircraftLatHome != null && !Double.isNaN(aircraftLatHome)) {
                    lat84home = aircraftLatHome;
                    lon84home = aircraftLngHome;
                    double[] appLatLngHome02 = GeoSysConversion.wgs84toGCJ02(lat84home, lon84home);
                    lat02home = appLatLngHome02[0];
                    lon02home = appLatLngHome02[1];
                }
            }

            /*if (lat84 == 0) {
                return;
            }*/


            try {
                mqttBean.setLatitudeWGS(lat84);
                mqttBean.setLongitudeWGS(lon84);
                mqttBean.setIsUAVOnline(1);
                mqttBean.setIsGSOnline(1);

                int windSpeed = (int) dji.keysdk.KeyManager.getInstance().getValue(dji.keysdk.FlightControllerKey.create(dji.keysdk.FlightControllerKey.WIND_SPEED));
                WindDirection windDirection = (WindDirection) dji.keysdk.KeyManager.getInstance().getValue(dji.keysdk.FlightControllerKey.create(dji.keysdk.FlightControllerKey.WIND_DIRECTION));
                mqttBean.setWindSpeed(windSpeed);
                mqttBean.setWindDirection(windDirection.value());

                PayloadData payloadData = MApplication.getInstance().getPayloadData();
                if (payloadData != null) {
                    /*ContextUtil.getHandler().post(new Runnable() {
                        @Override
                        public void run() {
                            ToastUtil.show("C02 :"+payloadData.getCO2Value1()+"   Tem1:"+payloadData.getTemperature1()
                                    +"  Humidity1:"+payloadData.getHumidity1()+" Humidity2:"+payloadData.getHumidity2()+"  pre1:"+payloadData.getPressure1()+"  pre2:"+payloadData.getPressure2());
                        }
                    });*/
                    GasDataInfo gasDataInfo = new GasDataInfo();
                    gasDataInfo.setHumidity(payloadData.getHumidity1());
                    gasDataInfo.setPressure(payloadData.getPressure1());
                    gasDataInfo.setTemperature(payloadData.getTemperature1());
                    AirData airData = new AirData();
                    airData.setCO2(payloadData.getCO2Value1());
                    gasDataInfo.setAirData(airData);
                    mqttBean.setGasDataInfo(gasDataInfo);
                }


                String beizhu = SpUtil.getBeiZhu();
                if (TextUtils.isEmpty(beizhu)) {
                    mqttBean.setUAVID(sn);
                } else {
                    mqttBean.setUAVID(beizhu);
                }

                if (DJIHelper.getInstance().isAircraftConnected()) {
                    mqttBean.setSN(DJIHelper.getInstance().getSerialNumber());
                }

                dji.common.gimbal.Attitude gimbalAttitude = (dji.common.gimbal.Attitude) KeyManager.getInstance().getValue(GimbalKey.create(GimbalKey.ATTITUDE_IN_DEGREES));
                if (gimbalAttitude != null) {
                    float gimbalPitch = gimbalAttitude.getPitch();
                    float gimbalRoll = gimbalAttitude.getRoll();
                    float gimbalYaw = gimbalAttitude.getYaw();
                    mqttBean.setGimbalPitch((int) gimbalPitch);
                    mqttBean.setGimbalYaw((int) gimbalYaw);
                    mqttBean.setGimbalRoll((int) gimbalRoll);
                }

                mqttBean.setUavPitch(0);
                mqttBean.setUavYaw(0);
                mqttBean.setUavRoll(0);
                SimpleDateFormat mSimpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                mqttBean.setTimestamp(mSimpleDateFormat.format(System.currentTimeMillis()));

                if (DJIHelper.getInstance().isAircraftConnected()) {
                    Float aircraftAltRel = (Float) KeyManager.getInstance().getValue(FlightControllerKey.create(FlightControllerKey.ALTITUDE));
                    mqttBean.setAltitude(aircraftAltRel);
                    Float vX = (Float) KeyManager.getInstance().getValue(FlightControllerKey.create(FlightControllerKey.VELOCITY_X));
                    Float vY = (Float) KeyManager.getInstance().getValue(FlightControllerKey.create(FlightControllerKey.VELOCITY_Y));
                    Float vZ = (Float) KeyManager.getInstance().getValue(FlightControllerKey.create(FlightControllerKey.VELOCITY_Z));
                    mqttBean.setHorizontalSpeed(calHSpeed(vX, vY));
                    mqttBean.setVerticalSpeed(vZ);
                } else {
                    mqttBean.setAltitude(0);
                    mqttBean.setHorizontalSpeed(0);
                    mqttBean.setVerticalSpeed(0);
                }
                Integer satelliteCount = (Integer) KeyManager.getInstance().getValue(FlightControllerKey.create(FlightControllerKey.SATELLITE_COUNT));
                mqttBean.setGPSCount(satelliteCount);

                mqttBean.setBatteryPercentage(batteryPercentage);

                // log.d("电池信息:"+mqttBean.getBatteries().size()+","+mqttBean.getBatteries().toString()+"\n",false);
                if (mqttClient.isConnected()) {
                    String js = new Gson().toJson(mqttBean);
                    MqttMessage mqttMessage = new MqttMessage(js.getBytes());
                    mqttMessage.setQos(0);
                    Log.e(TAG, "MQ Publish success :" + js);

                    mqttClient.publish(topicPublish, mqttMessage);
                }

            } catch (Exception e) {
                Log.d("MQ Publish", e.getMessage() + e.getCause() + e.getLocalizedMessage());
            }
        }
    }

    private float calHSpeed(float vX, float vY) {
        float hSpeed = 0;
        if (!Float.isNaN(vX) && !Float.isNaN(vY)) {
            hSpeed = (float) Math.sqrt(Math.pow(vX, 2) +
                    Math.pow(vY, 2));

        }
        return hSpeed;
    }

    private void disconnect() {
        //Log.e(TAG,"disconnect()");
        if (mqttClient != null) {
            try {
                mqttClient.disconnect();
                mqttClient.setCallback(null);
                mqttClient = null;
            } catch (MqttException e) {
                //Log.e(TAG,"MQtt#disconnect, exception: " + e.getLocalizedMessage());
            }
        }
    }

    public interface MQttListener {
        void onConnected();

        void onDisconnected();

        void onAuthenticationFailed();

        void onPreRelease();

        void onRelease();

        void onGetMsg(String topic, String msg);

        void onMsgDelivered();
    }

    private Runnable getBatteriesInfoRunnable = new Runnable() {
        @Override
        public void run() {
            getBatteriesInfo();
        }
    };

    public void getBatteriesInfo() {
        Log.e(TAG, "getBatteriesInfo: ");
        batteries = DJISDKManager.getInstance().getProduct().getBatteries();
        if (batteries == null) {
            ContextUtil.getHandler().postDelayed(getBatteriesInfoRunnable, 1500);
            return;
        }

        batteries.get(0).setStateCallback(new BatteryState.Callback() {
            @Override
            public void onUpdate(BatteryState batteryState) {
                if (batteryState != null) {
                    batteryPercentage = batteryState.getChargeRemainingInPercent();
                   /* batteryInfos.get(index).setCurrent(batteryState.getCurrent());
                    batteryInfos.get(index).setVoltage(batteryState.getVoltage());
                    batteryInfos.get(index).setBatteryPercent(batteryState.getChargeRemainingInPercent());
                    batteryInfos.get(index).setRemainingEnergy(batteryState.getChargeRemaining());
                    batteryInfos.get(index).setRemainingCapacity(batteryState.getFullChargeCapacity());
                    batteryInfos.get(index).setDesignCapacity(batteryState.getDesignCapacity());
                    batteryInfos.get(index).setTemperature(FormatUtil.getFloatByFormat(batteryState.getTemperature(), 2));
                    batteryInfos.get(index).setCycleCount(batteryState.getNumberOfDischarges());
                    batteryInfos.get(index).setLifetimePercent(batteryState.getLifetimeRemaining());*/
                    //Log.e(TAG, "onUpdate: index=" + index + "  getVoltage: " + batteryInfos.get(index).getVoltage());

                }
            }
        });

    }

}
