package com.skysys.fly.common.lbs.amap;

import com.amap.api.maps.model.LatLng;
import com.amap.api.services.core.LatLonPoint;
import com.amap.api.services.help.Tip;
import com.skysys.fly.common.lbs.bean.AppLatLng;
import com.skysys.fly.common.lbs.bean.TipInfo;

import java.util.ArrayList;
import java.util.List;


public class AMapTranslation {
    public static LatLng toSDK(AppLatLng appLatLng) {
        return new LatLng(appLatLng.getLat(), appLatLng.getLng());
    }

    public static List<LatLng> toSDK(List<AppLatLng> raw) {
        List<LatLng> result = new ArrayList<>();
        for (AppLatLng latLng : raw) {
            result.add(toSDK(latLng));
        }
        return result;
    }

    public static List<TipInfo> toAppTip(List<Tip> raw) {
        List<TipInfo> result = new ArrayList<>(raw.size());
        for (Tip item : raw) {
            TipInfo info = new TipInfo();
            info.setName(item.getName());
            info.setAddress(item.getAddress());
            info.setDistrict(item.getDistrict());
            info.setPoiID(item.getPoiID());
            LatLonPoint point = item.getPoint();
            if (point != null) {
                info.setPoint(new AppLatLng(point.getLatitude(), point.getLongitude()));
            }

            result.add(info);
        }
        return result;
    }

    public static AppLatLng toApp(LatLng latLng) {
        return new AppLatLng(latLng.latitude, latLng.longitude);
    }

    public static List<AppLatLng> toApp(List<LatLng> raw) {
        List<AppLatLng> result = new ArrayList<>();
        for (LatLng latLng : raw) {
            result.add(toApp(latLng));
        }
        return result;
    }
}
