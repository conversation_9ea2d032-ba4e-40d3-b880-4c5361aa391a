package com.skysys.fly.net.bean;

import com.alibaba.fastjson.annotation.JSONField;

public class AirData {


    @JSONField(name = "CL2(ppm)")
    float CL2; // 二氯
    @JSONField(name = "CO(ppm)")
    float CO; // 一氧化碳
    @J<PERSON>NField(name = "CO2(ppm)")
    float CO2; // 二氧化碳
    @JSONField(name = "CxHy(%)")
    float CxHy; // 烃
    @JSONField(name = "H2(ppm)")
    float H2; // 氢气
    @JSONField(name = "H2S(ppm)")
    float H2S; // 硫化氢
    @JSONField(name = "HCL(ppm)")
    float HCL; // 氯化氢
    @JSONField(name = "HCN(ppm)")
    float HCN; // 氰化氢
    @JSONField(name = "NH3(ppm)")
    float NH3; // 氨
    @JSONField(name = "NO2Z(ppm)")
    float NO2; // 二氧化氮
    @JSONField(name = "Ox(ppm)")
    float Ox; // 氧
    @J<PERSON>NField(name = "PH3(ppm)")
    float PH3; // 磷化氢
    @JSONField(name = "PM1.0(ug/m3)")
    float PM1_0; // PM1.0
    @JSONField(name = "PM10(ug/m3)")
    float PM10; //  PM10
    @JSONField(name = "PM2.5(ug/m3)")
    float PM2_5; // PM2.5
    @JSONField(name = "SO2(ppm)")
    float SO2; // 二氧化硫
    @JSONField(name = "TSP(mg/m3)")
    float TSP; // 悬浮物
    @JSONField(name = "VOCs(ppm)")
    float VOCs; // 挥发性有机物
    @JSONField(name = "[WR]SO2(ppm)")
    float WRSO2; // WR SO2
    @JSONField(name = "[HR]CH4(ppm)")
    float HRCH4; // 甲烷

    public float getCxHy() {
        return CxHy;
    }

    public void setCxHy(float cxHy) {
        CxHy = cxHy;
    }

    public float getH2() {
        return H2;
    }

    public void setH2(float h2) {
        H2 = h2;
    }

    public float getH2S() {
        return H2S;
    }

    public void setH2S(float h2S) {
        H2S = h2S;
    }

    public float getHCN() {
        return HCN;
    }

    public void setHCN(float HCN) {
        this.HCN = HCN;
    }

    public float getHCL() {
        return HCL;
    }

    public void setHCL(float HCL) {
        this.HCL = HCL;
    }

    public float getNH3() {
        return NH3;
    }

    public void setNH3(float NH3) {
        this.NH3 = NH3;
    }

    public float getNO2() {
        return NO2;
    }

    public void setNO2(float NO2) {
        this.NO2 = NO2;
    }

    public float getPH3() {
        return PH3;
    }

    public void setPH3(float PH3) {
        this.PH3 = PH3;
    }

    public float getOx() {
        return Ox;
    }

    public void setOx(float ox) {
        Ox = ox;
    }

    public float getPM1_0() {
        return PM1_0;
    }

    public void setPM1_0(float PM1_0) {
        this.PM1_0 = PM1_0;
    }

    public float getPM10() {
        return PM10;
    }

    public void setPM10(float PM10) {
        this.PM10 = PM10;
    }

    public float getPM2_5() {
        return PM2_5;
    }

    public void setPM2_5(float PM2_5) {
        this.PM2_5 = PM2_5;
    }

    public float getSO2() {
        return SO2;
    }

    public void setSO2(float SO2) {
        this.SO2 = SO2;
    }

    public float getTSP() {
        return TSP;
    }

    public void setTSP(float TSP) {
        this.TSP = TSP;
    }

    public float getVOCs() {
        return VOCs;
    }

    public void setVOCs(float VOCs) {
        this.VOCs = VOCs;
    }

    public float getWRSO2() {
        return WRSO2;
    }

    public void setWRSO2(float WRSO2) {
        this.WRSO2 = WRSO2;
    }

    public float getHRCH4() {
        return HRCH4;
    }

    public void setHRCH4(float HRCH4) {
        this.HRCH4 = HRCH4;
    }

    public float getCL2() {
        return CL2;
    }

    public void setCL2(float CL2) {
        this.CL2 = CL2;
    }

    public float getCO() {
        return CO;
    }

    public void setCO(float CO) {
        this.CO = CO;
    }

    public float getCO2() {
        return CO2;
    }

    public void setCO2(float CO2) {
        this.CO2 = CO2;
    }

}
