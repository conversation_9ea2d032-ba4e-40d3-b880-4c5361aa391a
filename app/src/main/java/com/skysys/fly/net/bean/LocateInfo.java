package com.skysys.fly.net.bean;

public class LocateInfo {

    private double longitude;
    private double latitude;
    private boolean isChina = true;

    public LocateInfo() {}

    public LocateInfo(double latitude, double longitude) {
        this.longitude = longitude;
        this.latitude = latitude;
    }

    public LocateInfo clone() {
        return new LocateInfo(this.latitude, this.longitude);
    }
    
    public static boolean isNull(LocateInfo info){
        if(info==null||Double.isNaN(info.getLatitude())||Double.isNaN(info.getLongitude())){return true;}
        else return false;
    }

    public double getLongitude() {
        return longitude;
    }
    public void setLongitude(double longitude) {
        this.longitude = longitude;
    }
    public double getLatitude() {
        return latitude;
    }
    public void setLatitude(double latitude) {
        this.latitude = latitude;
    }
    public boolean isChina() {
        return isChina;
    }
    public void setChina(boolean china) {
        isChina = china;
    }

    @Override
    public String toString() {
        return "[" + latitude + "," + longitude + ']';
    }

}
