package com.skysys.fly.net.bean;

import android.graphics.Point;

import com.skysys.fly.config.enums.LAUNCH_FLOW_STATE;
import com.skysys.fly.config.enums.MISSION_CHECK_CODE;

import java.util.ArrayList;
import java.util.List;

import dji.sdk.base.DJIDiagnostics;

public class MqttBean {

    private String UAVID;
    private String SN;
    private String missionID;
    private String missionBatch;
    private int waypointIndex;
    private float altitude;
    private double latitudeWGS;
    private double longitudeWGS;
    private int GPSCount;
    private int GPSLevel;
    private int isUAVOnline;
    private int isGSOnline;
    private int distanceStart;
    private float verticalSpeed;
    private float horizontalSpeed;
    private int gimbalPitch;
    private int gimbalYaw;
    private int gimbalRoll;
    private int uavPitch;
    private int uavYaw;
    private int uavRoll;
    private List<DJIDiagnostics> djiError;
    private int windSpeed;
    private int windDirection;
    private int batteryPercentage;
    private int batteryPercentageNeedToGoHome;
    private String timestamp;

    private GasDataInfo gasDataInfo;


    public MqttBean() {
    }

    public GasDataInfo getGasDataInfo() {
        return gasDataInfo;
    }

    public void setGasDataInfo(GasDataInfo gasDataInfo) {
        this.gasDataInfo = gasDataInfo;
    }

    public int getWindSpeed() {
        return windSpeed;
    }

    public void setWindSpeed(int windSpeed) {
        this.windSpeed = windSpeed;
    }

    public int getWindDirection() {
        return windDirection;
    }

    public void setWindDirection(int windDirection) {
        this.windDirection = windDirection;
    }

    public int getBatteryPercentage() {
        return batteryPercentage;
    }

    public void setBatteryPercentage(int batteryPercentage) {
        this.batteryPercentage = batteryPercentage;
    }

    public int getBatteryPercentageNeedToGoHome() {
        return batteryPercentageNeedToGoHome;
    }

    public void setBatteryPercentageNeedToGoHome(int batteryPercentageNeedToGoHome) {
        this.batteryPercentageNeedToGoHome = batteryPercentageNeedToGoHome;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getUAVID() {
        return UAVID;
    }

    public void setUAVID(String UAVID) {
        this.UAVID = UAVID;
    }

    public String getSN() {
        return SN;
    }

    public void setSN(String SN) {
        this.SN = SN;
    }

    public String getMissionID() {
        return missionID;
    }

    public void setMissionID(String missionID) {
        this.missionID = missionID;
    }

    public String getMissionBatch() {
        return missionBatch;
    }

    public void setMissionBatch(String missionBatch) {
        this.missionBatch = missionBatch;
    }

    public int getWaypointIndex() {
        return waypointIndex;
    }

    public void setWaypointIndex(int waypointIndex) {
        this.waypointIndex = waypointIndex;
    }

    public double getLatitudeWGS() {
        return latitudeWGS;
    }

    public void setLatitudeWGS(double latitudeWGS) {
        this.latitudeWGS = latitudeWGS;
    }

    public double getLongitudeWGS() {
        return longitudeWGS;
    }

    public void setLongitudeWGS(double longitudeWGS) {
        this.longitudeWGS = longitudeWGS;
    }

    public float getAltitude() {
        return altitude;
    }

    public void setAltitude(float altitude) {
        this.altitude = altitude;
    }

    public int getGPSCount() {
        return GPSCount;
    }

    public void setGPSCount(int GPSCount) {
        this.GPSCount = GPSCount;
    }

    public int getGPSLevel() {
        return GPSLevel;
    }

    public void setGPSLevel(int GPSLevel) {
        this.GPSLevel = GPSLevel;
    }

    public int getIsUAVOnline() {
        return isUAVOnline;
    }

    public void setIsUAVOnline(int isUAVOnline) {
        this.isUAVOnline = isUAVOnline;
    }

    public int getIsGSOnline() {
        return isGSOnline;
    }

    public void setIsGSOnline(int isGSOnline) {
        this.isGSOnline = isGSOnline;
    }

    public float getVerticalSpeed() {
        return verticalSpeed;
    }

    public void setVerticalSpeed(float verticalSpeed) {
        this.verticalSpeed = verticalSpeed;
    }

    public float getHorizontalSpeed() {
        return horizontalSpeed;
    }

    public void setHorizontalSpeed(float horizontalSpeed) {
        this.horizontalSpeed = horizontalSpeed;
    }

    public int getGimbalPitch() {
        return gimbalPitch;
    }

    public void setGimbalPitch(int gimbalPitch) {
        this.gimbalPitch = gimbalPitch;
    }

    public int getGimbalYaw() {
        return gimbalYaw;
    }

    public void setGimbalYaw(int gimbalYaw) {
        this.gimbalYaw = gimbalYaw;
    }

    public int getGimbalRoll() {
        return gimbalRoll;
    }

    public void setGimbalRoll(int gimbalRoll) {
        this.gimbalRoll = gimbalRoll;
    }

    public int getUavPitch() {
        return uavPitch;
    }

    public void setUavPitch(int uavPitch) {
        this.uavPitch = uavPitch;
    }

    public int getUavYaw() {
        return uavYaw;
    }

    public void setUavYaw(int uavYaw) {
        this.uavYaw = uavYaw;
    }

    public int getUavRoll() {
        return uavRoll;
    }

    public void setUavRoll(int uavRoll) {
        this.uavRoll = uavRoll;
    }

    public int getDistanceStart() {
        return distanceStart;
    }

    public void setDistanceStart(int distanceStart) {
        this.distanceStart = distanceStart;
    }

    public List<DJIDiagnostics> getDjiError() {
        return djiError;
    }

    public void setDjiError(List<DJIDiagnostics> djiError) {
        this.djiError = djiError;
    }

    @Override
    public String toString() {
        return "MqttBean{" +
                "UAVID='" + UAVID + '\'' +
                ", SN='" + SN + '\'' +
                ", missionID='" + missionID + '\'' +
                ", missionBatch='" + missionBatch + '\'' +
                ", waypointIndex=" + waypointIndex +
                ", latitudeWGS=" + latitudeWGS +
                ", longitudeWGS=" + longitudeWGS +
                ", altitude=" + altitude +
                ", GPSCount=" + GPSCount +
                ", GPSLevel=" + GPSLevel +
                ", isUAVOnline=" + isUAVOnline +
                ", isGSOnline=" + isGSOnline +
                ", verticalSpeed=" + verticalSpeed +
                ", horizontalSpeed=" + horizontalSpeed +
                ", gimbalPitch=" + gimbalPitch +
                ", gimbalYaw=" + gimbalYaw +
                ", gimbalRoll=" + gimbalRoll +
                ", uavPitch=" + uavPitch +
                ", uavYaw=" + uavYaw +
                ", uavRoll=" + uavRoll +
                ", distanceStart=" + distanceStart +
                ", djiError=" + djiError +
                ", windSpeed=" + windSpeed +
                ", windDirection=" + windDirection +
                ", batteryPercentage=" + batteryPercentage +
                ", batteryPercentageNeedToGoHome=" + batteryPercentageNeedToGoHome +
                ", gasDataInfo=" + gasDataInfo +
                ", timestamp='" + timestamp + '\'' +
                '}';
    }
}
