package com.skysys.fly.net.bean;

public class GasDataInfo {


    float humidity;
    float pressure;
    float temperature;
    AirData airData;

    public float getHumidity() {
        return humidity;
    }

    public void setHumidity(float humidity) {
        this.humidity = humidity;
    }

    public float getPressure() {
        return pressure;
    }

    public void setPressure(float pressure) {
        this.pressure = pressure;
    }

    public float getTemperature() {
        return temperature;
    }

    public void setTemperature(float temperature) {
        this.temperature = temperature;
    }

    public AirData getAirData() {
        return airData;
    }

    public void setAirData(AirData airData) {
        this.airData = airData;
    }

}
