package com.skysys.fly.data.preference;

import android.content.Context;
import android.content.SharedPreferences;

import com.skysys.fly.common.json.JsonUtil;
import com.skysys.fly.common.voice.SoundPlayerBean;
import com.skysys.fly.data.mission.MissionDetail;

public class SpUtil {
    private static SharedPreferences sp;

    public static void init(Context context) {
        sp = context.getSharedPreferences("skysys", Context.MODE_PRIVATE);
    }


    public static boolean getRemoteMode() {
        return sp.getBoolean("Remote_mode", false);
    }

    /**
     * 美国手/日本手/中国手
     *
     * @param remoteMode 遥控器操作模式
     */
    public static void setRemoteMode(boolean remoteMode) {
        sp.edit().putBoolean("Remote_mode", remoteMode).apply();
    }

    public static int getRecorderIndex() {
        return sp.getInt("recorder_index", 1);
    }


    public static void setRecorderIndex(int index) {
        sp.edit().putInt("recorder_index", index).apply();
    }


    public static boolean getDeviceRecorder() {
        return sp.getBoolean("device_recorder", false);
    }

    public static void setDeviceRecorder(boolean isOpen) {
        sp.edit().putBoolean("device_recorder", isOpen).apply();
    }

    public static boolean getRadarUIShow() {
        return sp.getBoolean("RadarUIShow", false);
    }

    public static void setRadarUIShow(boolean RadarUIShow) {
        sp.edit().putBoolean("RadarUIShow", RadarUIShow).apply();
    }

    public static SoundPlayerBean getPlayerSound() {
        String playerSound = sp.getString("SoundPlayerBean", null);
        return JsonUtil.fromJson(playerSound, SoundPlayerBean.class);
    }

    public static void setPlayerSound(SoundPlayerBean playerSound) {
        sp.edit().putString("SoundPlayerBean", JsonUtil.toJson(playerSound)).apply();
    }

    public static String getIP(){
        return sp.getString("ip","tcp://mqtt-cn-0pp0s45s703.mqtt.aliyuncs.com:1883");
    }

    public static void setIp(String ip){
        sp.edit().putString("ip", ip).apply();
    }

    public static String getBeiZhu(){
        return sp.getString("beizhu","");
    }

    public static void setBeiZhu(String beizhu){
        sp.edit().putString("beizhu", beizhu).apply();
    }

    public static String getMissionBatch(){
        return sp.getString("missionBatch","");
    }

    public static void setMissionBatch(String missionBatch){
        sp.edit().putString("missionBatch", missionBatch).apply();
    }

    public static void setMissionData(String missionBatch, MissionDetail missionDetail){
        sp.edit().putString(missionBatch, JsonUtil.toJson(missionDetail)).apply();
    }

    public static MissionDetail getMissionData(String missionBatch){
        String detail = sp.getString(missionBatch, null);
        return JsonUtil.fromJson(detail, MissionDetail.class);
    }

    public static void remove(String missionBatch){
        sp.edit().remove(missionBatch).apply();
    }


    /**
     * app是否需要向DJI服务器请求校验
     *
     * @param registered isRegister
     */
    public static void setDJIRegistered(boolean registered) {
        sp.edit().putBoolean("dji_registered", registered).apply();
    }

    public static void setDefaultHDSignalChannel() {
        sp.edit().putBoolean("isset_defaultchannel", true).apply();
    }

    public static boolean getIsSetDefaultHDSignalChannel() {
        return sp.getBoolean("isset_defaultchannel", false);
    }

    public static int getSpeed(){
        return sp.getInt("speed",10);
    }

    public static void setSpeed(int speed){
        sp.edit().putInt("speed", speed).apply();
    }

}
