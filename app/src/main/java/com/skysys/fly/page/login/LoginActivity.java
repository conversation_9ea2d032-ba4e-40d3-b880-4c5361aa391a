package com.skysys.fly.page.login;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Matrix;
import android.graphics.Rect;
import android.os.Bundle;
import android.text.InputType;
import android.text.method.HideReturnsTransformationMethod;
import android.text.method.PasswordTransformationMethod;
import android.util.Log;
import android.view.View;
import android.view.WindowManager;
import android.widget.CompoundButton;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.skysys.fly.R;
import com.skysys.fly.databinding.ActivityLoginBinding;
import com.skysys.fly.net.api.NetApi;
import com.skysys.fly.net.common.BaseCallback;
import com.skysys.fly.page.BaseActivity;
import com.skysys.fly.page.home.HomeActivity;
import com.skysys.fly.util.FormatUtil;
import com.skysys.fly.util.SharedPreferencesUtils;
import com.skysys.fly.util.StringUtil;

import okhttp3.RequestBody;


public class LoginActivity extends BaseActivity implements View.OnClickListener{

    private boolean issave = false;
    private boolean isshow = false;
    private boolean iscode = false;

    private  int width;
    private int height;
    //private MyObserver<LoginBean> myObserver;
    static String url = "https://flv2.bn.netease.com/videolib1/1811/26/OqJAZ893T/HD/OqJAZ893T-mobile.mp4";
    private String password = "password";
    private boolean isusernamelogin = true;
    private ActivityLoginBinding binding;
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = DataBindingUtil.setContentView(this,R.layout.activity_login);
        WindowManager wm = (WindowManager) this
                .getSystemService(Context.WINDOW_SERVICE);
         width = wm.getDefaultDisplay().getWidth();
         height = wm.getDefaultDisplay().getHeight();
        /*Constant.windowwidth = width;
        Constant.windowheight = height;*/
        //setImmerseBar(false);
        initView();
        keyboardLayout(binding.reTop,binding.etPassword,true);
    }

    private void initView() {
        //binding.tvVerioncode.setText("当前版本："+ UAVM_INFO.getInstance().versionname);
        showinfo();
        binding.checkbox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton compoundButton, boolean b) {
                issave = b;
            }
        });

        //initLocalVideo();
        //binding.imgFrist.setImageDrawable(getThumbnail("video"));
        binding.etPassword.setTransformationMethod(PasswordTransformationMethod.getInstance());

        binding.login.setOnClickListener(this);
        binding.imgIsshow.setOnClickListener(this);
        binding.tvSwitch.setOnClickListener(this);
        binding.tvCode.setOnClickListener(this);
        binding.tvLoginCode.setOnClickListener(this);
        binding.tvLoginPassword.setOnClickListener(this);
    }
    private void showinfo(){
        if (!SharedPreferencesUtils.getParam(this,password,"").equals("")){binding.etPassword.setText( String.valueOf(SharedPreferencesUtils.getParam(this,password,"")));}
        if (!SharedPreferencesUtils.getParam(this,"mobile","").equals("")){
            binding.etMobile.setText( String.valueOf(SharedPreferencesUtils.getParam(this,"mobile","")));
            issave = true;
            binding.checkbox.setChecked(true);
        }
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()){
            case R.id.login:
               /* if (mqttUtil != null) {
                    mqttUtil.disconnect();
                }*/
                if (checkParas()) {
                    login();
                }
                //jumtToMainPage();
                break;
            case R.id.img_isshow:
                isshow = !isshow;
                if (!isshow){
                    binding.imgIsshow.setImageResource(R.drawable.vision_track_watch_nor_gray);
                    binding.etPassword.setTransformationMethod(PasswordTransformationMethod.getInstance());
                }else {
                    binding.imgIsshow.setImageResource(R.drawable.vision_track_watch_pre);
                    binding.etPassword.setTransformationMethod(HideReturnsTransformationMethod.getInstance());
                }
                break;
            case R.id.tv_switch:
                iscode = !iscode;
                if (iscode){
                    binding.tvPassword.setText("验证码");
                    binding.tvCode.setVisibility(View.VISIBLE);
                    password = "verifyCode";
                    if (!isshow){
                        isshow = true;
                        binding.imgIsshow.setImageResource(R.drawable.vision_track_watch_pre);
                        binding.etPassword.setTransformationMethod(HideReturnsTransformationMethod.getInstance());
                    }
                }else {
                    binding.tvCode.setVisibility(View.GONE);
                    binding.tvPassword.setText("密码");
                    password = "password";
                }
                break;
           /* case R.id.tv_code:
                if (!UavmInfo2.getInstance().issendcode ){
                    if (checkParas()){
                        getphonecode();
                    }
                }else {
                    Toast.makeText(LoginActivity.this,"不可重复点击",Toast.LENGTH_SHORT).show();
                }
                break;*/
            case R.id.tv_login_code:
                isusernamelogin = false;
                binding.etMobile.setInputType(InputType.TYPE_CLASS_PHONE);
                binding.tvName.setText("手机号");
                iscode = true;
                binding.tvCode.setVisibility(View.VISIBLE);
                binding.tvPassword.setText("验证码");
                password = "verifyCode";
                binding.tvLoginCode.setTextColor(getResources().getColor(R.color.ijk_transparent_dark));
                binding.tvLoginPassword.setTextColor(getResources().getColor(R.color.black));
                binding.tvLoginCode.setBackgroundResource(R.color.transparent);
                binding.tvLoginPassword.setBackgroundResource(R.drawable.login_top2);
                binding.etPassword.setHint("请输入验证码");
                binding.imgIsshow.setVisibility(View.GONE);
                if (!isshow){
                    isshow = true;
                    binding.imgIsshow.setImageResource(R.drawable.vision_track_watch_pre);
                    binding.etPassword.setTransformationMethod(HideReturnsTransformationMethod.getInstance());
                }
                binding.linSave.setVisibility(View.GONE);
                binding.etPassword.setText("");
                break;
            case R.id.tv_login_password:
                isusernamelogin = true;
                binding.etMobile.setInputType(InputType.TYPE_CLASS_TEXT);
                binding.tvName.setText("用户名");
                iscode = false;
                if (isshow){
                    isshow = false;
                    binding.imgIsshow.setImageResource(R.drawable.vision_track_watch_nor_gray);
                    binding.etPassword.setTransformationMethod(PasswordTransformationMethod.getInstance());
                }
                binding.tvCode.setVisibility(View.GONE);
                binding.imgIsshow.setVisibility(View.VISIBLE);
                binding.tvLoginCode.setTextColor(getResources().getColor(R.color.black));
                binding.tvLoginPassword.setTextColor(getResources().getColor(R.color.ijk_transparent_dark));
                binding.tvLoginCode.setBackgroundResource(R.drawable.login_top4);
                binding.tvLoginPassword.setBackgroundResource(R.color.transparent);
                binding.tvPassword.setText("密码");
                password = "password";
                binding.etPassword.setHint("请输入密码");
                binding.linSave.setVisibility(View.VISIBLE);
                if (!SharedPreferencesUtils.getParam(this,password,"").equals("")){binding.etPassword.setText( String.valueOf(SharedPreferencesUtils.getParam(this,password,"")));}

                break;
        }
    }
    private boolean checkParas() {

        String str_password = binding.etPassword.getText().toString();
        String str_mobile = binding.etMobile.getText().toString();
        if (!FormatUtil.is_reg_mobleNumber(str_mobile) && !isusernamelogin) {
            Toast.makeText(LoginActivity.this, "手机号码格式有误", Toast.LENGTH_SHORT).show();
            return false;
        }
        if (StringUtil.isEmpty(str_password) && !iscode) {
            Toast.makeText(LoginActivity.this, "密码输入不能为空", Toast.LENGTH_SHORT).show();
            return false;
        }
        return true;
    }
    private void login() {
        /*String str_password = binding.etPassword.getText().toString();
        String str_mobile = binding.etMobile.getText().toString();
        myObserver = new MyObserver<LoginBean>(this,"登录中") {
            @Override
            public void onSuccess(LoginBean result) {
                MyApp.getInstance().globalInfo.setLoginToken(result.getToken());
                MyApp.getInstance().globalInfo.setLoginMobile(result.getMobilePhone());
                MyApp.getInstance().globalInfo.setLoginTime(new Date());
                MyApp.getInstance().globalInfo.setAccessKeyId(result.getSocketIOAccount().getAccessKeyId());
                MyApp.getInstance().globalInfo.setAccessKeySecret(result.getSocketIOAccount().getAccessKeySecret());
                UavmInfo2.getInstance().str_mobile =  str_mobile;
                System.out.println(result);
                if (issave){
                    SharedPreferencesUtils.setParam(LoginActivity.this,password,str_password);
                    SharedPreferencesUtils.setParam(LoginActivity.this,"mobile",str_mobile);
                }else {
                    SharedPreferencesUtils.setParam(LoginActivity.this,password,"");
                    SharedPreferencesUtils.setParam(LoginActivity.this,"mobile","");}
                jumtToMainPage();
            }
            @Override
            public void onSuccesscode(int errorcode) {
            }
            @Override
            public void onFailure(Throwable e, String errorMsg) {
                Toast.makeText(LoginActivity.this, errorMsg, Toast.LENGTH_SHORT).show();
            }
        };
        Map<String, String> paras = new HashMap<>();
        if (!isusernamelogin){
            paras.put("mobile", str_mobile);
        }else {
            paras.put("name", str_mobile);
        }

        paras.put(password, str_password);
        RequestUtils.login(this, myObserver, paras);*/

        String str_password = binding.etPassword.getText().toString();
        String str_mobile = binding.etMobile.getText().toString();
        /*Map<String, Object> paras = new HashMap<>();
        if (!isusernamelogin){
            paras.put("mobile", str_mobile);
        }else {
            paras.put("name", str_mobile);
        }
        paras.put(password, str_password);
        NetApi.post("/login", paras, new BaseCallback() {
            @Override
            protected void onSuccess(String response) {
                Log.e("TAG", "onSuccess: "+response);
            }

            @Override
            protected void onFailure(Throwable t){
                Log.e("TAG", "onFailure: "+t.getLocalizedMessage());
            }
        });*/Map<String, Object> paras = new HashMap<>();
            paras.put("userName", str_mobile);
        paras.put("password", str_password);
        NetApi.postRaw("/qcp/users/login", paras, new BaseCallback() {
            @Override
            protected void onSuccess(String response) {
                Log.e("TAG", "onSuccess: "+response);
            }

            @Override
            protected void onFailure(Throwable t){
                Log.e("TAG", "onFailure: "+t.getLocalizedMessage());
            }
        });
    }


    private void jumtToMainPage() {
        startActivity(new Intent(LoginActivity.this, HomeActivity.class));
        overridePendingTransition(R.anim.anim_in,R.anim.anim_out);
        finish();
    }

    /**
     * 解决在页面底部置输入框，输入法弹出遮挡部分输入框的问题
     *
     * @param root       页面根元素
     * @param editLayout 被软键盘遮挡的输入框editText
     * @param hasFocus   当前editText是否获取焦点
     */
    public static void keyboardLayout(final View root,
                                      final View editLayout, boolean hasFocus) {
        // TODO Auto-generated method stub
        root.getViewTreeObserver().addOnGlobalLayoutListener(() -> {
            // TODO Auto-generated method stub
            Rect rect = new Rect();
            //获取root在窗体的可视区域
            root.getWindowVisibleDisplayFrame(rect);
            //获取root在窗体的不可视区域高度(被其他View遮挡的区域高度)
            int rootInVisibleHeigh = root.getRootView().getHeight() - rect.bottom;
            //若不可视区域高度大于100，则键盘显示
            if (hasFocus && rootInVisibleHeigh > 100) {
//                Log.v("hjb", "不可视区域高度大于100，则键盘显示");
                int[] location = new int[2];
                //获取editLayout在窗体的坐标
                editLayout.getLocationInWindow(location);
                //计算root滚动高度，使editLayout在可见区域
                int srollHeight = (location[1] + editLayout.getHeight()) - rect.bottom;
                root.scrollTo(0, srollHeight);
            } else {
                //键盘隐藏
//                Log.v("hjb", "不可视区域高度小于100，则键盘隐藏");
                root.scrollTo(0, 0);
            }
        });
    }


}
