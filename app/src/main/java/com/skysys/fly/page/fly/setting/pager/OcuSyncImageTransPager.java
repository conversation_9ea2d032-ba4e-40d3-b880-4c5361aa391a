package com.skysys.fly.page.fly.setting.pager;

import static dji.common.airlink.OcuSyncFrequencyBand.FREQUENCY_BAND_2_DOT_4_GHZ;
import static dji.common.airlink.OcuSyncFrequencyBand.FREQUENCY_BAND_5_DOT_8_GHZ;
import static dji.common.airlink.OcuSyncFrequencyBand.FREQUENCY_BAND_DUAL;

import android.view.LayoutInflater;
import android.view.View;
import android.widget.ListView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;

import com.skysys.fly.R;
import com.skysys.fly.common.ContextUtil;
import com.skysys.fly.common.drone.DJIHelper;
import com.skysys.fly.common.drone.key.AirLinkKey;
import com.skysys.fly.common.drone.key.KeyListener;
import com.skysys.fly.common.drone.key.KeyManager;
import com.skysys.fly.databinding.MenuHdMavicPagerBinding;
import com.skysys.fly.page.fly.AircraftActivity;
import com.skysys.fly.page.fly.setting.AircraftSettingFragment;
import com.skysys.fly.page.fly.setting.pager.detail.MavicSignalChannel;
import com.skysys.fly.view.adapter.EasyAdapter;
import com.skysys.fly.view.adapter.EasyHolder;

import java.util.ArrayList;

import dji.common.airlink.OcuSyncFrequencyBand;
import dji.common.error.DJIError;
import dji.common.util.CommonCallbacks;
import dji.sdk.airlink.AirLink;

public class OcuSyncImageTransPager extends BasePager implements View.OnClickListener{
    private static final String G_24 = "2.4G";
    private static final String G_58 = "5.8G";
    private static final String DUAL = "双频";
    private MenuHdMavicPagerBinding binding;
    private MavicSignalChannel mavicSignalChannel;
    private AirLink airLink;
    private String frequencyName;
    private int currentPosition;
    private EasyAdapter easyAdapter;
    private ListView mListView;
    private  TextView tvHdFrequency;
    private RelativeLayout rlHdFrequencyPop;
    private  RelativeLayout rlHdFrequency;
    private final ArrayList<String> listFrequency = new ArrayList<>();
    private KeyListener<OcuSyncFrequencyBand[]> OcuSyncListener;

    public OcuSyncImageTransPager(AircraftSettingFragment fragment) {
        super(fragment);
    }

    @Override
    public void initData() {
        isLoading = true;
        activityMenuPresenter.setIsPrevious(false);
        isLoading = true;
        tvTitle.setText(ContextUtil.getString(R.string.hd_set_pager));

        binding = DataBindingUtil.inflate(LayoutInflater.from(fragment.getActivity()), R.layout.menu_hd_mavic_pager, null, false);
        binding.setActivityMenuPresenter(activityMenuPresenter);

        mavicSignalChannel = new MavicSignalChannel(binding, (AircraftActivity)fragment.getActivity());
        activityMenuPresenter.currentMavicSignalChannel(mavicSignalChannel);

        flContainer.addView(binding.getRoot());

        tvHdFrequency = binding.menuHdFrequency.tvHdPrequency;
        rlHdFrequency = binding.menuHdFrequency.rlHdFrequency;
        rlHdFrequencyPop = binding.menuHdFrequency.rlHdFrequencyPop;
        rlHdFrequencyPop.setOnClickListener(this);
        mListView = (ListView) View.inflate(ContextUtil.getApplicationContext(), R.layout.item_listview, null);

        airLink = DJIHelper.getInstance().getAirLink();
        getDataRate();
        getFrequency();
    }

    /*获取码率*/
    private void getDataRate(){
        if (airLink != null && airLink.getOcuSyncLink() != null) {
            airLink.getOcuSyncLink().setVideoDataRateCallback(speed -> ContextUtil.getHandler().post(() -> binding.tvDatarate.setText(String.format("%.2f", speed) + "Mbps")));
        }
    }

    /*监听频段*/
    private void getFrequency(){
        OcuSyncListener = new KeyListener<OcuSyncFrequencyBand[]>() {
            @Override
            protected void onValueChanged(@Nullable OcuSyncFrequencyBand[] old, @Nullable final OcuSyncFrequencyBand[] ocuSyncFrequencyBands) {
                if (ocuSyncFrequencyBands != null) {
                    ContextUtil.getHandler().post(() -> {
                        if (ocuSyncFrequencyBands.length <= 1) {
                            rlHdFrequency.setVisibility(View.GONE);
                        } else {
                            rlHdFrequency.setVisibility(View.VISIBLE);
                        }
                    });

                    listFrequency.clear();
                    for (OcuSyncFrequencyBand ocuSyncFrequencyBand : ocuSyncFrequencyBands) {
                        int value = ocuSyncFrequencyBand.getValue();
                        switch (value) {
                            case 0:
                                listFrequency.add(DUAL);
                                break;
                            case 1:
                                listFrequency.add(G_24);
                                break;
                            case 2:
                                listFrequency.add(G_58);
                                break;
                        }

                    }

                    getCurrentFrequency();
                }
            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(AirLinkKey.createOcuSyncLinkKey(AirLinkKey.SUPPORTED_FREQUENCY_BANDS), OcuSyncListener);
    }

    /*获取当前工作频段*/
    private void getCurrentFrequency() {
        if (airLink.getOcuSyncLink() != null) {
            airLink.getOcuSyncLink().getFrequencyBand(new CommonCallbacks.CompletionCallbackWith<OcuSyncFrequencyBand>() {
                @Override
                public void onSuccess(OcuSyncFrequencyBand ocuSyncFrequencyBand) {
                    int value = ocuSyncFrequencyBand.getValue();
                    switch (value) {
                        case 0:
                            frequencyName = DUAL;
                            break;
                        case 1:
                            frequencyName = G_24;
                            break;
                        case 2:
                            frequencyName = G_58;
                            break;
                    }

                    for (int i = 0; i < listFrequency.size(); i++) {
                        String s = listFrequency.get(i);
                        if (s.equals(frequencyName)) {
                            currentPosition = i;
                        }
                    }
                    ContextUtil.getHandler().post(() -> tvHdFrequency.setText(frequencyName));

                    setListIntoMode(currentPosition);
                }

                @Override
                public void onFailure(DJIError djiError) {

                }
            });
        }
    }

    private void setListIntoMode(int integer) {
        easyAdapter = new EasyAdapter(ContextUtil.getApplicationContext(), listFrequency) {
            @Override
            public EasyHolder getHolder(int type) {
                return new EasyHolder() {
                    private TextView tv_text_item;
                    @Override
                    public int getLayout() {
                        return R.layout.one_center_item;
                    }

                    @Override
                    public View createView(int position) {
                        tv_text_item = view.findViewById(R.id.tv_center_item);
                        return view;
                    }

                    @Override
                    public void refreshView(int position, Object item) {
                        if (position==currentPosition){
                            tv_text_item.setTextColor(ContextUtil.getColor(R.color.colorBright));
                        }else {
                            tv_text_item.setTextColor(ContextUtil.getColor(R.color.white));
                        }
                        String ite = (String) item;
                        tv_text_item.setText(ite);
                    }
                };
            }
        };

        mListView.setAdapter(easyAdapter);
        mListView.setSelection(integer);
        mListView.setVerticalScrollBarEnabled(false);
        mListView.setOnItemClickListener((parent, view, position, id) -> {
            currentPosition=position;
            String s = listFrequency.get(position);
            switch (s) {
                case G_24:
                    setCurrentFrequency(FREQUENCY_BAND_2_DOT_4_GHZ,G_24);
                    break;
                case DUAL:
                    setCurrentFrequency(FREQUENCY_BAND_DUAL, DUAL);
                    break;
                case G_58:
                    setCurrentFrequency(FREQUENCY_BAND_5_DOT_8_GHZ, G_58);
                    break;
            }
        });
    }

    /*设置工作频段*/
    private void setCurrentFrequency(OcuSyncFrequencyBand frequencyBand, final String g24) {
        if(airLink.getOcuSyncLink() != null){
            airLink.getOcuSyncLink().setFrequencyBand(frequencyBand, djiError -> {
                if (djiError==null){
                    ContextUtil.getHandler().post(() -> {
                        easyAdapter.notifyDataSetChanged();
                        tvHdFrequency.setText(g24);
                        ((AircraftActivity) ContextUtil.getCurrentActivity()).dismissPopupWindow();
                    });

                }
            });
        }
    }

    @Override
    public void removeListener() {
        if(isLoading){
            mavicSignalChannel.remove();
            KeyManager.getInstance().removeListener(OcuSyncListener);
        }
    }

    @Override
    public void onClick(View v) {
        View anchor= tvHdFrequency;
        ((AircraftActivity)fragment.getActivity()).showPopup(anchor,mListView, listFrequency.size());
    }

    @Override
    public void isConnect(boolean connect) {
        if(isLoading){
            if (connect){
                rlHdFrequencyPop.setClickable(true);
            }else {
                rlHdFrequencyPop.setClickable(false);
            }
        }
    }
}
