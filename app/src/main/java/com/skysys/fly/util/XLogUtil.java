package com.skysys.fly.util;

import android.text.TextUtils;

import com.elvishew.xlog.XLog;


public class XLogUtil<T> {

    public T t;
    public String tag;

    public XLogUtil(T t){
        this.t = t;
    }

    public XLogUtil(String tag){
        this.tag = tag;
    }

    public MyLogger getLogger(){
        if(!TextUtils.isEmpty(tag)){
            return new MyLogger(XLog.tag(tag).build());
        }else{
            return new MyLogger(XLog.tag(t!=null?t.getClass().getSimpleName():"noDefinedTag").build());
        }
    }

    public T getT() {
        return t;
    }

    public void setT(T t) {
        this.t = t;
    }
}
