<?xml version="1.0" encoding="utf-8"?>

<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item>

        <shape android:shape="rectangle">
            <corners android:radius="5dp" />

            <solid android:color="@color/uxsdk_white" />

            <stroke
                android:width="1dp"
                android:color="@color/uxsdk_gray" />
        </shape>
    </item>

<!--    <item>-->
<!--        &lt;!&ndash; 倒三角 &ndash;&gt;-->
<!--        <rotate-->
<!--            android:fromDegrees="45"-->
<!--            android:pivotX="100%"-->
<!--            android:pivotY="0%">-->
<!--            <shape android:shape="rectangle">-->
<!--                <solid android:color="@color/uxsdk_blue"/>-->
<!--            </shape>-->
<!--        </rotate>-->
<!--    </item>-->

</layer-list>